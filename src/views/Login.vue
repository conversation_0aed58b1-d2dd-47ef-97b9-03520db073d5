<template>
  <div class="overflow-hidden relative w-screen h-screen">
    <!-- 加载状态覆盖层 -->
    <div v-if="isLoading" class="flex absolute inset-0 z-20 justify-center items-center bg-background">
      <div class="flex flex-col gap-4 items-center">
        <div class="w-12 h-12 rounded-full border-4 animate-spin border-primary border-r-transparent"></div>
        <div class="text-center">
          <p class="text-muted-foreground">正在加载登录页面...</p>
        </div>
      </div>
    </div>

    <!-- 错误提示覆盖层 -->
    <div v-if="loginError" class="flex absolute inset-0 z-10 justify-center items-center bg-background">
      <div class="p-6 mx-auto w-full max-w-md">
        <div class="p-6 text-center rounded-lg border shadow-lg bg-card">
          <div class="mb-4">
            <p class="mb-2 font-medium text-destructive">登录失败</p>
            <p class="text-sm text-muted-foreground">{{ loginError }}</p>
          </div>
          <Button @click="retryLogin" class="w-full">
            重试登录
          </Button>
        </div>
      </div>
    </div>

    <!-- 全屏登录 iframe -->
    <iframe ref="loginIframe" :src="loginUrl" class="block w-screen h-screen border-0"
      sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation" @load="handleIframeLoad"
      @error="handleIframeError" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/composables/useAuth'

// 使用认证 composable
const {
  loginUrl,
  error,
  isLoading,
  clearAuth,
} = useAuth()

// 为了向后兼容，保持原有的变量名
const loginError = error

// 组件状态
const loginIframe = ref<HTMLIFrameElement>()

/**
 * 处理 iframe 加载完成
 */
const handleIframeLoad = () => {
  console.log('📱 登录页面加载完成')
}

/**
 * 处理 iframe 加载错误
 */
const handleIframeError = () => {
  console.error('❌ 登录页面加载失败')
}

/**
 * 重试登录
 */
const retryLogin = () => {
  // 清除错误状态并重新加载 iframe
  clearAuth()
  if (loginIframe.value) {
    loginIframe.value.src = loginUrl.value
  }
}
</script>