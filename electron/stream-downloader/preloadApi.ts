import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { StreamDownloadConfig, DownloadTask, DownloadApiResponse } from "./types";

// 下载 API 接口
export interface DownloadPreloadApi {
  // 创建下载任务
  createTask: (fileName?: string, savePath?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;

  // 创建多个下载任务
  createTasks: (
    downloads: Array<{
      fileName?: string;
      savePath?: string;
      metadata?: Record<string, string>;
    }>
  ) => Promise<DownloadApiResponse>;

  // 通过对话框选择保存位置创建下载任务
  createTaskWithDialog: (defaultFileName?: string, metadata?: Record<string, string>) => Promise<DownloadApiResponse>;

  // 开始下载
  startDownload: (taskId: string) => Promise<DownloadApiResponse>;

  // 暂停下载
  pauseDownload: (taskId: string) => Promise<DownloadApiResponse>;

  // 恢复下载
  resumeDownload: (taskId: string) => Promise<DownloadApiResponse>;

  // 取消下载
  cancelDownload: (taskId: string) => Promise<DownloadApiResponse>;

  // 重试下载
  retryDownload: (taskId: string) => Promise<DownloadApiResponse>;

  // 删除任务
  deleteTask: (taskId: string) => Promise<DownloadApiResponse>;

  // 获取所有任务
  getAllTasks: () => Promise<DownloadApiResponse>;

  // 获取指定任务
  getTask: (taskId: string) => Promise<DownloadApiResponse>;

  // 获取活跃任务
  getActiveTasks: () => Promise<DownloadApiResponse>;

  // 更新配置
  updateConfig: (config: Partial<StreamDownloadConfig>) => Promise<DownloadApiResponse>;

  // 清理已完成的任务
  clearCompletedTasks: () => Promise<DownloadApiResponse>;

  // 批量开始下载
  startBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;

  // 批量暂停下载
  pauseBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;

  // 批量恢复下载
  resumeBatch: (taskIds: string[]) => Promise<DownloadApiResponse>;

  // 获取下载统计信息
  getStats: () => Promise<DownloadApiResponse>;

  // 获取未完成的任务
  getUnfinishedTasks: () => Promise<DownloadApiResponse>;

  // 清空所有任务
  clearAllTasks: () => Promise<DownloadApiResponse>;

  // 显示文件夹选择对话框
  showSelectFolderDialog: (defaultPath?: string) => Promise<DownloadApiResponse>;

  // 获取默认下载路径
  getDefaultDownloadPath: () => Promise<DownloadApiResponse>;
}

// 下载事件监听器接口
export interface DownloadEventListeners {
  onDownloadTaskCreated: (callback: (taskId: string, task: DownloadTask) => void) => void;
  onDownloadTaskProgress: (callback: (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void) => void;
  onDownloadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => void;
  onDownloadTaskCompleted: (callback: (taskId: string) => void) => void;
  onDownloadTaskError: (callback: (taskId: string, error: string) => void) => void;
  removeAllListeners: (channel: string) => void;
}

// 简化的下载 API 接口（整合了 API 调用和事件监听器）
export interface SimplifiedDownloadPreloadApi extends DownloadPreloadApi, DownloadEventListeners {}

/**
 * 创建下载 API
 */
export function createDownloadApi(): DownloadPreloadApi {
  return {
    // 创建下载任务
    createTask: (fileName?: string, savePath?: string, metadata?: Record<string, string>) => ipcRenderer.invoke("download-create-task", fileName, savePath, metadata),

    // 创建多个下载任务
    createTasks: (
      downloads: Array<{
        fileName?: string;
        savePath?: string;
        metadata?: Record<string, string>;
      }>
    ) => ipcRenderer.invoke("download-create-tasks", downloads),

    // 通过对话框选择保存位置创建下载任务
    createTaskWithDialog: (defaultFileName?: string, metadata?: Record<string, string>) => ipcRenderer.invoke("download-create-task-with-dialog", defaultFileName, metadata),

    // 开始下载
    startDownload: (taskId: string) => ipcRenderer.invoke("download-start", taskId),

    // 暂停下载
    pauseDownload: (taskId: string) => ipcRenderer.invoke("download-pause", taskId),

    // 恢复下载
    resumeDownload: (taskId: string) => ipcRenderer.invoke("download-resume", taskId),

    // 取消下载
    cancelDownload: (taskId: string) => ipcRenderer.invoke("download-cancel", taskId),

    // 重试下载
    retryDownload: (taskId: string) => ipcRenderer.invoke("download-retry", taskId),

    // 删除任务
    deleteTask: (taskId: string) => ipcRenderer.invoke("download-delete-task", taskId),

    // 获取所有任务
    getAllTasks: () => ipcRenderer.invoke("download-get-all-tasks"),

    // 获取指定任务
    getTask: (taskId: string) => ipcRenderer.invoke("download-get-task", taskId),

    // 获取活跃任务
    getActiveTasks: () => ipcRenderer.invoke("download-get-active-tasks"),

    // 更新配置
    updateConfig: (config: Partial<StreamDownloadConfig>) => ipcRenderer.invoke("download-update-config", config),

    // 清理已完成的任务
    clearCompletedTasks: () => ipcRenderer.invoke("download-clear-completed-tasks"),

    // 批量开始下载
    startBatch: (taskIds: string[]) => ipcRenderer.invoke("download-start-batch", taskIds),

    // 批量暂停下载
    pauseBatch: (taskIds: string[]) => ipcRenderer.invoke("download-pause-batch", taskIds),

    // 批量恢复下载
    resumeBatch: (taskIds: string[]) => ipcRenderer.invoke("download-resume-batch", taskIds),

    // 获取下载统计信息
    getStats: () => ipcRenderer.invoke("download-get-stats"),

    // 获取未完成的任务
    getUnfinishedTasks: () => ipcRenderer.invoke("download-get-unfinished-tasks"),

    // 清空所有任务
    clearAllTasks: () => ipcRenderer.invoke("download-clear-all-tasks"),

    // 显示文件夹选择对话框
    showSelectFolderDialog: (defaultPath?: string) => ipcRenderer.invoke("download-show-select-folder-dialog", defaultPath),

    // 获取默认下载路径
    getDefaultDownloadPath: () => ipcRenderer.invoke("download-get-default-path"),
  };
}

/**
 * 创建下载事件监听器
 */
export function createDownloadEventListeners(): DownloadEventListeners {
  return {
    // 下载任务创建事件
    onDownloadTaskCreated: (callback: (taskId: string, task: DownloadTask) => void) => {
      ipcRenderer.on("download-task-created", (_event, taskId, task) => callback(taskId, task));
    },

    // 下载任务进度事件
    onDownloadTaskProgress: (callback: (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void) => {
      ipcRenderer.on("download-task-progress", (_event, taskId, progress, bytesDownloaded, bytesTotal) => callback(taskId, progress, bytesDownloaded, bytesTotal));
    },

    // 下载任务状态变化事件
    onDownloadTaskStatusChanged: (callback: (taskId: string, status: string, error?: string) => void) => {
      ipcRenderer.on("download-task-status-changed", (_event, taskId, status, error) => callback(taskId, status, error));
    },

    // 下载任务完成事件
    onDownloadTaskCompleted: (callback: (taskId: string) => void) => {
      ipcRenderer.on("download-task-completed", (_event, taskId) => callback(taskId));
    },

    // 下载任务错误事件
    onDownloadTaskError: (callback: (taskId: string, error: string) => void) => {
      ipcRenderer.on("download-task-error", (_event, taskId, error) => callback(taskId, error));
    },

    // 移除所有监听器
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel);
    },
  };
}

/**
 * 创建简化的下载 API（整合了 API 调用和事件监听器）
 */
export function createSimplifiedDownloadPreloadApi(): SimplifiedDownloadPreloadApi {
  const api = createDownloadApi();
  const listeners = createDownloadEventListeners();

  return {
    ...api,
    ...listeners,
  };
}
