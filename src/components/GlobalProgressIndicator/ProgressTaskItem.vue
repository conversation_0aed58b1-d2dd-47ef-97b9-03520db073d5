<template>
  <div class="space-y-1">
    <div class="flex items-center justify-between text-xs">
      <div class="flex items-center flex-1 min-w-0 gap-2">
        <component :is="getTaskIcon()" :class="cn(
          'flex-shrink-0 w-3 h-3',
          getTaskIconClass()
        )" />
        <span class="font-medium truncate">{{ task.fileName }}</span>
        <span v-if="task.size" class="flex-shrink-0 text-muted-foreground whitespace-nowrap">({{ task.size }})</span>
        <span v-if="task.status === 'error'" class="text-xs text-red-500 whitespace-nowrap">失败</span>
        <span v-if="task.status === 'paused'" class="text-xs text-yellow-500 whitespace-nowrap">已暂停</span>
      </div>
      <div class="flex items-center flex-shrink-0 gap-1">
        <span v-if="task.status !== 'error'" class="text-muted-foreground whitespace-nowrap">
          {{ Math.round(task.progress) }}%
        </span>

        <!-- 暂停按钮 (仅上传中时显示) -->
        <Button v-if="task.status === 'in-progress'" variant="ghost" size="sm" @click="$emit('pause', task.id)"
          class="w-5 h-5 p-0" title="暂停">
          <PauseIcon class="w-3 h-3" />
        </Button>

        <!-- 恢复按钮 (仅暂停时显示) -->
        <Button v-if="task.status === 'paused'" variant="ghost" size="sm" @click="$emit('resume', task.id)"
          class="w-5 h-5 p-0" title="恢复">
          <PlayIcon class="w-3 h-3" />
        </Button>

        <!-- 重试按钮 -->
        <Button v-if="task.status === 'error'" variant="ghost" size="sm" @click="$emit('retry', task.id)"
          class="w-5 h-5 p-0" title="重试">
          <RefreshCwIcon class="w-3 h-3" />
        </Button>

        <!-- 取消/移除按钮 -->
        <Button variant="ghost" size="sm" @click="handleAction" class="w-5 h-5 p-0"
          :title="task.status === 'error' ? '移除' : '取消'">
          <XIcon class="w-3 h-3" />
        </Button>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="task.status !== 'error'" class="w-full h-1 rounded-full bg-secondary">
      <div class="h-1 transition-all duration-300 rounded-full" :class="getProgressBarClass()"
        :style="{ width: `${task.progress}%` }" />
    </div>

    <!-- 错误信息 -->
    <div v-if="task.error" class="p-1 text-xs text-red-500 rounded bg-red-50">
      {{ task.error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  AlertCircle as AlertCircleIcon,
  X as XIcon,
  RefreshCw as RefreshCwIcon,
  Pause as PauseIcon,
  Play as PlayIcon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import type { ProgressTask } from '@/composables/useGlobalProgress'

interface Props {
  task: ProgressTask
}

const props = defineProps<Props>()

const emit = defineEmits<{
  cancel: [taskId: string]
  remove: [taskId: string]
  retry: [taskId: string]
  pause: [taskId: string]
  resume: [taskId: string]
}>()

const getTaskIcon = () => {
  if (props.task.status === 'error') return AlertCircleIcon
  if (props.task.status === 'paused') return PauseIcon
  return props.task.type === 'upload' ? UploadIcon : DownloadIcon
}

const getTaskIconClass = () => {
  if (props.task.status === 'error') return 'text-red-500'
  if (props.task.status === 'paused') return 'text-yellow-500'
  return 'text-primary animate-pulse'
}

const getProgressBarClass = () => {
  if (props.task.status === 'paused') return 'bg-yellow-500'
  return 'bg-primary'
}

const handleAction = () => {
  if (props.task.status === 'error') {
    emit('remove', props.task.id)
  } else {
    emit('cancel', props.task.id)
  }
}
</script>