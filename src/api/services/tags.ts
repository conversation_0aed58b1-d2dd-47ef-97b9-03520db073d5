import { request } from "@/api/http";
import type { ApiResponse } from "@/api/http";

// 标签项数据类型定义
export interface Tag {
  id: number;
  item_value: string;
}

// 标签类别数据类型定义
export interface TagCategory {
  category_id: number;
  category_name: string;
  update_user: string;
  update_time: string;
  tags: Tag[];
}

// 标签列表响应数据（data直接是TagCategory数组）
export type TagListResponse = TagCategory[];

// 标签值项（用于编辑标签API）
export interface TagValue {
  name: string;
  id: number; // -1 表示新增标签，正数表示现有标签的ID
}

// 编辑标签参数
export interface EditTagParams {
  category_id: string; // 分类ID（字符串格式）
  tag_values: TagValue[];
}

// 扁平化的标签项，用于表格显示
export interface TagItem {
  id: string;
  tag_id: number;
  category_id: number;
  category_name: string;
  tag_value: string;
  update_user: string;
  update_time: string;
}

// 游戏项目数据类型定义（根据API返回的数据结构）
export interface GameItem {
  id: number;
  game_name: string;
  update_user: string;
  update_time: string;
}

// 游戏项目数据类型定义（用于前端显示，适配原有组件）
export interface GameProject {
  id: string | number;
  name: string;
  updatedBy: string;
  updatedAt: string;
  createdAt?: string;
}

// 游戏列表响应数据
export type GameListResponse = GameItem[];

// 创建游戏项目参数
export interface CreateGameProjectParams {
  name: string;
}

// 更新游戏项目参数
export interface UpdateGameProjectParams {
  id: string | number;
  name?: string;
}

// 标签相关 API
export const tagsApi = {
  // 获取标签列表
  getTagList(): Promise<ApiResponse<TagListResponse>> {
    return request.get("/netdisk/getTagList");
  },

  // 编辑标签
  editTag(data: EditTagParams): Promise<ApiResponse> {
    return request.post("/netdisk/editTag", data);
  },
};

// 游戏项目相关 API
export const gameProjectsApi = {
  // 获取游戏项目列表
  getGameProjectsList(): Promise<ApiResponse<GameListResponse>> {
    return request.get("/netdisk/getGameList");
  },

  // 创建游戏项目
  createGameProject(data: CreateGameProjectParams): Promise<ApiResponse> {
    return request.get("/netdisk/addGame", {
      params: { game_name: data.name },
    });
  },

  // 更新游戏项目
  updateGameProject(data: UpdateGameProjectParams): Promise<ApiResponse> {
    return request.get("/netdisk/editGame", {
      params: {
        id: data.id,
        game_name: data.name,
      },
    });
  },

  // 删除游戏项目
  deleteGameProject(id: string | number): Promise<ApiResponse> {
    return request.get("/netdisk/deleteGame", {
      params: { id },
    });
  },
};

// 统一导出
export default {
  tags: tagsApi,
  gameProjects: gameProjectsApi,
};
