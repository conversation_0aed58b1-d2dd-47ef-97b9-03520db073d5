import { request } from "@/api/http";
import type { ApiResponse } from "@/api/http";
import type { GetUserInfoResponse } from "@/types/user";

interface DirectoryContentsData {
  category_id: number | string;
  id: number | string;
  page: number | string;
  limit: number | string;
  // 筛选参数
  sortBy?: string; // 排序方式: name, size, date
  sortOrder?: string; // 排序顺序: asc, desc
  fileTypes?: string[]; // 文件类型筛选
  sizeRange?: string; // 文件大小筛选: small, medium, large, xlarge
  search?: string; // 搜索关键词
  [key: string]: any;
}

// 获取用户信息
function getUserInfo(): Promise<ApiResponse<GetUserInfoResponse["data"]>> {
  return request.get("/auth/login");
}

// 获取SideBar目录列表
function getTopCategoryDirectories(): Promise<ApiResponse> {
  return request.get("/netdisk/getTopCategoryDirectories");
}

// 获取文件目录列表
function getDirectoryContents(data: DirectoryContentsData): Promise<ApiResponse> {
  return request.post("/netdisk/getDirectoryContents", data);
}

// 获取所有筛选选项
function getAllFilterOptions(category_id: number): Promise<ApiResponse> {
  return request.get("/netdisk/getAllFilterOptions", { params: { category_id } });
}

export default {
  getUserInfo,
  getTopCategoryDirectories,
  getDirectoryContents,
  getAllFilterOptions,
};
