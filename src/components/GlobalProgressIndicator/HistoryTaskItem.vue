<template>
  <div class="p-2 border rounded bg-card">
    <div class="flex items-center gap-2">
      <!-- 任务类型图标和状态 -->
      <div class="flex items-center gap-1">
        <component :is="getTaskIcon()" :class="cn(
          'flex-shrink-0 w-3 h-3',
          getTaskIconClass()
        )" />
        <span v-if="task.status === 'error'" class="w-2 h-2 bg-red-500 rounded-full" :title="task.error"></span>
        <span v-else-if="task.status === 'cancelled'" class="w-2 h-2 bg-gray-400 rounded-full" title="已取消"></span>
        <span v-else class="w-2 h-2 bg-green-500 rounded-full" title="已完成"></span>
      </div>

      <!-- 文件信息 -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium truncate">{{ task.fileName }}</span>
          <span v-if="task.size" class="text-xs text-muted-foreground whitespace-nowrap">
            ({{ task.size }})
          </span>


        </div>
        <div class="flex items-center gap-2 text-xs text-muted-foreground">
          <span>{{ formatDate(task.endTime) }}</span>
          <span>·</span>
          <span>{{ formatDuration(task.duration) }}</span>
          <span v-if="task.status === 'error'" class="text-red-500">失败</span>
          <span v-else-if="task.status === 'cancelled'" class="text-gray-500">已取消</span>

        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center gap-1">
        <Button v-if="task.status === 'error'" variant="ghost" size="sm" @click="$emit('retry', task.id)"
          class="h-6 w-6 p-0" title="重试">
          <RotateCcwIcon class="w-3 h-3" />
        </Button>
        <Button variant="ghost" size="sm" @click="$emit('remove', task.id)" class="h-6 w-6 p-0" title="删除">
          <XIcon class="w-3 h-3" />
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {
  Upload as UploadIcon,
  Download as DownloadIcon,
  RotateCcw as RotateCcwIcon,
  X as XIcon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { formatDate, formatDuration } from '@/lib/upload-utils'
import type { HistoryTask } from '@/composables/useGlobalProgress'

interface Props {
  task: HistoryTask
}

const props = defineProps<Props>()

defineEmits<{
  retry: [taskId: string]
  remove: [taskId: string]
}>()

// 获取任务图标
const getTaskIcon = () => {
  return props.task.type === 'upload' ? UploadIcon : DownloadIcon
}

// 获取任务图标样式
const getTaskIconClass = () => {
  if (props.task.status === 'error') {
    return 'text-red-500'
  } else if (props.task.status === 'cancelled') {
    return 'text-gray-400'
  } else {
    return props.task.type === 'upload' ? 'text-blue-500' : 'text-green-500'
  }
}


</script>