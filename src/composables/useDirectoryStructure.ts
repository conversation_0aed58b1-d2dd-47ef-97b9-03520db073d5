import { ref } from "vue";
import { Home, Folder } from "lucide-vue-next";
import { formatFileSize } from "@/lib/upload-utils";
import commonApi from "@/api/services/common";
import type { ApiResponse } from "@/api/http";
import { useSidebarStore } from "@/store/sidebar";
import type { FileItemType, FolderItemType, ItemType } from "@/types/files";

// 接口返回的原始数据类型
export interface ApiFileItem {
  id: number;
  parent_id: number;
  file_name: string;
  is_folder: 0 | 1;
  category_id: number;
  node: string;
  game_project: string;
  class: string;
  tag: string;
  ver: string;
  source: number;
  metadata_id: number;
  create_user: string;
  create_user_name: string;
  create_time: string;
  update_user: string;
  update_time: string;
  is_binding: string;
  has_ue_project: string;
  has_animation: string;
  thumbnail_small: string;
  thumbnail_medium: string;
  preview: string;
  format: string;
  size: number;
  size_human: string; // 人类可读的文件大小格式，如 "9.04 MB"
  mime_type: string;
  // 自定义属性字段
  category_fields?: Record<string, any>;
}

export interface ApiPathData {
  id: number;
  name: string;
}

export interface ApiDirectoryResponse {
  data: ApiFileItem[];
  total: number;
  page: string;
  limit: string;
  pathData: ApiPathData[];
  pathDataStr: string;
  category_id: string;
  category_key: string;
}

// 使用统一的文件项类型（来自 files.ts）
export type FileItem = FileItemType;
export type FolderItem = FolderItemType;
export type DirectoryItem = ItemType;

export interface BreadcrumbItem {
  name: string;
  path: string;
  icon: any;
  iconColor: string;
  id?: string;
}

// 筛选参数类型定义
export interface FilterParams {
  sortBy?: string;
  sortOrder?: string;
  fileTypes?: string[];
  sizeRange?: string;
  search?: string;
}

// 数据转换函数
function transformApiDataToDirectoryItems(apiData: ApiFileItem[]): DirectoryItem[] {
  return apiData.map((item) => {
    const modifiedDate = new Date(item.update_time || item.create_time);

    if (item.is_folder === 1) {
      // 文件夹
      const folderData = {
        id: item.id.toString(),
        name: item.file_name,
        type: "folder" as const,
        itemCount: 0, // API 没有直接提供子项数量，可能需要额外计算
        modifiedAt: modifiedDate,
        uploader: item.create_user_name || item.create_user,
        // 保存分类信息
        category_id: item.category_id,
        // 缩略图字段 - 支持封面文件夹
        thumbnailSmall: item.thumbnail_small,
        thumbnailMedium: item.thumbnail_medium,
        thumbnail_video: (item as any).thumbnail_video, // 使用类型断言处理可能的新字段
        preview: item.preview,
        // 保存自定义属性字段
        category_fields: item.category_fields,
      };

      return folderData;
    } else {
      // 文件
      const fileType = getFileTypeFromMimeType(item.mime_type) || getFileTypeFromFormat(item.format);

      return {
        id: item.id.toString(),
        name: item.file_name,
        size: item.size_human || formatFileSize(item.size), // 优先使用后端格式，回退到前端格式化
        size_human: item.size_human, // 使用后端提供的人类可读格式
        type: fileType,
        modifiedAt: modifiedDate,
        uploader: item.create_user_name || item.create_user,
        // 保存分类信息
        category_id: item.category_id,
        mimeType: item.mime_type,
        version: item.ver,
        thumbnailSmall: item.thumbnail_small,
        thumbnailMedium: item.thumbnail_medium,
        preview: item.preview,
        format: item.format,
        rawSize: item.size,
        hasAnimation: item.has_animation === "Y",
        hasUeProject: item.has_ue_project === "Y",
        gameProject: item.game_project,
        class: item.class,
        tag: item.tag,
        // 保存自定义属性字段
        category_fields: item.category_fields,
      };
    }
  });
}

// 根据 MIME 类型推断文件类型
function getFileTypeFromMimeType(mimeType: string): string {
  if (!mimeType) return "file";

  if (mimeType.startsWith("image/")) return "image";
  if (mimeType.startsWith("video/")) return "video";
  if (mimeType.startsWith("audio/")) return "audio";
  if (mimeType.includes("pdf")) return "pdf";
  if (mimeType.includes("word") || mimeType.includes("document")) return "document";
  if (mimeType.includes("sheet") || mimeType.includes("excel")) return "spreadsheet";
  if (mimeType.includes("presentation") || mimeType.includes("powerpoint")) return "presentation";
  if (mimeType.includes("zip") || mimeType.includes("rar") || mimeType.includes("archive")) return "archive";

  return "file";
}

// 根据文件格式推断文件类型
function getFileTypeFromFormat(format: string): string {
  if (!format) return "file";

  const imageFormats = ["jpg", "jpeg", "png", "gif", "bmp", "svg", "webp"];
  const videoFormats = ["mp4", "avi", "mov", "mkv", "wmv", "flv", "webm"];
  const audioFormats = ["mp3", "wav", "aac", "ogg", "flac", "m4a"];
  const modelFormats = ["fbx", "obj", "dae", "blend", "3ds", "max"];
  const archiveFormats = ["zip", "rar", "7z", "tar", "gz"];

  const lowerFormat = format.toLowerCase();

  if (imageFormats.includes(lowerFormat)) return "image";
  if (videoFormats.includes(lowerFormat)) return "video";
  if (audioFormats.includes(lowerFormat)) return "audio";
  if (modelFormats.includes(lowerFormat)) return "model";
  if (archiveFormats.includes(lowerFormat)) return "archive";
  if (lowerFormat === "pdf") return "pdf";

  return "file";
}

export function useDirectoryStructure() {
  const loading = ref(false);
  const error = ref<string | null>(null);
  const sidebarStore = useSidebarStore();

  // 获取目录内容
  const getDirectoryContents = async (
    categoryId: number | string,
    parentId: number | string = 1,
    page: number | string = 1,
    limit: number | string = 20,
    filters?: FilterParams
  ): Promise<{
    items: DirectoryItem[];
    total: number;
    page: string;
    limit: string;
    pathData: ApiPathData[];
    pathDataStr: string;
    categoryId: string;
    categoryKey: string;
  }> => {
    loading.value = true;
    error.value = null;

    try {
      // 构建请求参数
      const requestData = {
        category_id: categoryId,
        id: parentId,
        page,
        limit,
        ...filters, // 包含筛选参数
      };

      const response: ApiResponse<ApiDirectoryResponse> = await commonApi.getDirectoryContents(requestData);

      if (response.code === 0 && response.data) {
        const directoryData = response.data;
        const items = transformApiDataToDirectoryItems(directoryData.data);

        return {
          items,
          total: directoryData.total,
          page: directoryData.page,
          limit: directoryData.limit,
          pathData: directoryData.pathData,
          pathDataStr: directoryData.pathDataStr,
          categoryId: directoryData.category_id,
          categoryKey: directoryData.category_key,
        };
      } else {
        throw new Error(response.msg || "获取目录内容失败");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "获取目录内容时发生未知错误";
      error.value = errorMessage;

      return {
        items: [],
        total: 0,
        page: "1",
        limit: "20",
        pathData: [],
        pathDataStr: "",
        categoryId: "",
        categoryKey: "",
      };
    } finally {
      loading.value = false;
    }
  };

  // 生成面包屑导航
  const getBreadcrumbs = (pathData: ApiPathData[], categoryKey?: string, categoryId?: string | number): BreadcrumbItem[] => {
    const home = {
      name: "首页",
      path: "/resources",
      icon: Home,
      iconColor: "text-primary",
    };

    const breadcrumbs: BreadcrumbItem[] = [home];

    // 根据 category_key 或 category_id 从 sidebar store 中查找分类信息
    let categoryItem = null;
    if (categoryKey) {
      categoryItem = sidebarStore.sidebarItems.find((item) => item.category_key === categoryKey);
    } else if (categoryId) {
      categoryItem = sidebarStore.sidebarItems.find((item) => item.category_id === Number(categoryId));
    }

    // 如果找到分类信息，添加到面包屑中
    if (categoryItem) {
      breadcrumbs.push({
        name: categoryItem.name,
        path: `/resources/${categoryItem.category_key}?categoryId=${categoryItem.category_id}`,
        icon: categoryItem.icon,
        iconColor: categoryItem.iconColor || "text-muted-foreground",
        id: categoryItem.id.toString(),
      });
    }

    // 如果有路径数据，基于 API 返回的路径数据生成面包屑
    if (pathData && pathData.length > 0 && categoryItem) {
      // 跳过第一个路径项（通常是分类根目录），从第二个开始处理文件夹层级
      const folderPathData = pathData.slice(1);

      folderPathData.forEach((pathItem, index) => {
        // 构建当前层级的完整路径
        // 需要包含从根目录到当前层级的所有文件夹名称
        let folderPath = "";

        // 构建文件夹路径：将从根到当前层级的所有文件夹名称用 '/' 连接
        for (let i = 0; i <= index; i++) {
          const folderName = encodeURIComponent(folderPathData[i].name);
          folderPath += (i === 0 ? "" : "/") + folderName;
        }

        // 构建完整的路径
        const fullPath = `/resources/${categoryItem.category_key}/${folderPath}`;

        // 构建查询参数
        const query = `categoryId=${categoryItem.category_id}&parentId=${pathItem.id}`;

        breadcrumbs.push({
          name: pathItem.name,
          path: `${fullPath}?${query}`,
          icon: Folder,
          iconColor: "text-muted-foreground",
          id: pathItem.id.toString(),
        });
      });
    }

    return breadcrumbs;
  };

  // 根据 category_key 获取分类信息
  const getCategoryInfo = (categoryKey: string) => {
    return sidebarStore.sidebarItems.find((item) => item.category_key === categoryKey);
  };

  // 根据 category_id 获取分类信息
  const getCategoryInfoById = (categoryId: number | string) => {
    return sidebarStore.sidebarItems.find((item) => item.category_id === Number(categoryId));
  };

  return {
    loading,
    error,
    getDirectoryContents,
    getBreadcrumbs,
    getCategoryInfo,
    getCategoryInfoById,
  };
}
