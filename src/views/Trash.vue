<template>
  <div class="p-8">
    <div class="max-w-7xl mx-auto">
      <div class="mb-8">
        <div class="flex items-center mb-4">
          <Trash2 class="w-8 h-8 text-muted-foreground mr-3" />
          <h1 class="text-3xl font-bold text-foreground">回收站</h1>
        </div>
        <p class="text-lg text-muted-foreground">已删除的文件将在30天后自动清理</p>
      </div>

      <div v-if="trashedFiles.length === 0" class="text-center py-16">
        <Trash2 class="w-16 h-16 text-muted-foreground mx-auto mb-4" />
        <h3 class="text-lg font-medium text-muted-foreground mb-2">回收站为空</h3>
        <p class="text-muted-foreground">没有找到已删除的文件</p>
      </div>

      <div v-else class="space-y-4">
        <div class="flex justify-between items-center">
          <p class="text-sm text-muted-foreground">{{ trashedFiles.length }} 个已删除的文件</p>
          <div class="space-x-2">
            <Button variant="outline" size="sm" @click="restoreAll">
              <RotateCcw class="w-4 h-4 mr-2" />
              全部恢复
            </Button>
            <Button variant="destructive" size="sm" @click="emptyTrash">
              <Trash2 class="w-4 h-4 mr-2" />
              清空回收站
            </Button>
          </div>
        </div>

        <div class="bg-card rounded-lg border border-border">
          <div class="p-4 border-b border-border">
            <div class="grid grid-cols-12 gap-4 text-sm font-medium text-muted-foreground">
              <div class="col-span-6">文件名</div>
              <div class="col-span-2">类型</div>
              <div class="col-span-2">删除时间</div>
              <div class="col-span-2">操作</div>
            </div>
          </div>

          <div class="divide-y divide-border">
            <div v-for="file in trashedFiles" :key="file.id" class="p-4 hover:bg-accent transition-colors">
              <div class="grid grid-cols-12 gap-4 items-center">
                <div class="col-span-6 flex items-center">
                  <component :is="file.icon" class="w-5 h-5 text-muted-foreground mr-3" />
                  <div>
                    <p class="text-sm font-medium">{{ file.name }}</p>
                    <p class="text-xs text-muted-foreground">{{ file.size }}</p>
                  </div>
                </div>
                <div class="col-span-2 text-sm text-muted-foreground">{{ file.type }}</div>
                <div class="col-span-2 text-sm text-muted-foreground">{{ file.deletedAt }}</div>
                <div class="col-span-2 flex space-x-2">
                  <Button variant="ghost" size="sm" @click="restoreFile(file.id)">
                    <RotateCcw class="w-4 h-4" />
                  </Button>
                  <Button variant="ghost" size="sm" @click="deleteForever(file.id)">
                    <X class="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Trash2, RotateCcw, X, Box, Image, Music, FileText } from 'lucide-vue-next'
import Button from '@/components/ui/button/Button.vue'

interface TrashedFile {
  id: string
  name: string
  type: string
  size: string
  deletedAt: string
  icon: any
}

const trashedFiles = ref<TrashedFile[]>([
  {
    id: '1',
    name: '角色模型_旧版.fbx',
    type: '3D模型',
    size: '15.2 MB',
    deletedAt: '2小时前',
    icon: Box
  },
  {
    id: '2',
    name: '测试贴图.jpg',
    type: '图片',
    size: '2.1 MB',
    deletedAt: '1天前',
    icon: Image
  },
  {
    id: '3',
    name: '背景音乐_demo.mp3',
    type: '音频',
    size: '5.8 MB',
    deletedAt: '3天前',
    icon: Music
  },
  {
    id: '4',
    name: '项目说明.txt',
    type: '文档',
    size: '12 KB',
    deletedAt: '1周前',
    icon: FileText
  }
])

const restoreFile = (id: string) => {
  const index = trashedFiles.value.findIndex(file => file.id === id)
  if (index > -1) {
    trashedFiles.value.splice(index, 1)
    // 这里应该调用API恢复文件
    console.log('恢复文件:', id)
  }
}

const deleteForever = (id: string) => {
  if (confirm('确定要永久删除这个文件吗？此操作无法撤销。')) {
    const index = trashedFiles.value.findIndex(file => file.id === id)
    if (index > -1) {
      trashedFiles.value.splice(index, 1)
      // 这里应该调用API永久删除文件
      console.log('永久删除文件:', id)
    }
  }
}

const restoreAll = () => {
  if (confirm('确定要恢复所有文件吗？')) {
    trashedFiles.value = []
    // 这里应该调用API恢复所有文件
    console.log('恢复所有文件')
  }
}

const emptyTrash = () => {
  if (confirm('确定要清空回收站吗？此操作无法撤销。')) {
    trashedFiles.value = []
    // 这里应该调用API清空回收站
    console.log('清空回收站')
  }
}
</script>