// 文件和文件夹项目类型定义

export interface FileItemType {
  id: string;
  name: string;
  size: string;
  size_human?: string; // 人类可读的文件大小格式，如 "9.04 MB"
  type: string;
  modifiedAt: Date;
  // 必要的分类信息
  category_id?: number;
  // 扩展属性 - 支持 API 返回的所有属性
  uploader?: string;
  mimeType?: string;
  version?: string;
  encoding?: string;
  checksum?: string;
  resolution?: string;
  duration?: string;
  bitrate?: string;
  dimensions?: string;
  colorSpace?: string;
  pageCount?: number;
  author?: string;
  title?: string;
  thumbnailSmall?: string;
  thumbnailMedium?: string;
  preview?: string;
  format?: string;
  rawSize?: number;
  hasAnimation?: boolean;
  hasUeProject?: boolean;
  gameProject?: string;
  class?: string;
  tag?: string;
  // 允许其他动态属性
  [key: string]: any;
}

export interface FolderItemType {
  id: string;
  name: string;
  type: "folder";
  itemCount: number;
  modifiedAt: Date;
  path?: string;
  // 必要的分类信息
  category_id?: number;
  // 扩展属性
  uploader?: string;
  children?: ItemType[];
  // 允许其他动态属性
  [key: string]: any;
}

export type ItemType = FileItemType | FolderItemType;

// 操作相关类型
export interface ItemAction {
  action: string;
  item: ItemType;
}

export interface BatchAction {
  action: string;
  items: ItemType[];
}
