<template>
  <div class="border-b scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
    <!-- 筛选器容器 - 支持水平滚动 -->
    <div class="flex overflow-x-auto items-center p-2 space-x-4" ref="containerRef">
      <!-- 筛选器组 -->
      <div v-for="(filterGroup, groupIndex) in visibleFilters" :key="groupIndex" class="relative flex-shrink-0">
        <Button variant="outline" size="sm" class="relative justify-start font-normal text-left filter-button min-w-20"
          :class="getSelectedCount(groupIndex) > 0 ? 'border-primary' : ''" @click="toggleDropdown(groupIndex)"
          :ref="el => setButtonRef(el, groupIndex)">
          <div class="flex items-center w-full">
            <span class="mr-2 text-sm font-medium">{{ filterGroup.label }}:</span>
            <div class="flex flex-1 items-center min-w-0">
              <!-- 多选：显示选中的标签 -->
              <div v-if="filterGroup.type === 'multiselect' && getSelectedCount(groupIndex) > 0"
                class="flex flex-1 items-center space-x-1 min-w-0">
                <div class="flex overflow-hidden items-center space-x-1">
                  <span v-for="tag in getDisplayTags(groupIndex)" :key="tag"
                    class="inline-flex items-center px-1.5 py-0.5 text-xs whitespace-nowrap rounded border filter-tag bg-primary/10 text-primary border-primary/20">
                    <span class="truncate max-w-20" :title="tag">{{ tag }}</span>
                    <button @click.stop="removeTag(groupIndex, tag)" class="flex-shrink-0 ml-1 hover:text-destructive"
                      :title="`移除 ${tag}`">
                      <X class="w-3 h-3" />
                    </button>
                  </span>
                </div>
                <span v-if="getSelectedCount(groupIndex) > 3"
                  class="flex-shrink-0 text-xs whitespace-nowrap text-muted-foreground">
                  +{{ getSelectedCount(groupIndex) - 3 }}更多
                </span>
              </div>
              <!-- 单选：直接显示选中的值 -->
              <div v-else-if="filterGroup.type === 'select' && getSelectedCount(groupIndex) > 0"
                class="flex flex-1 items-center min-w-0">
                <span class="flex-1 text-sm truncate" :title="getSelectedLabel(groupIndex)">
                  {{ getSelectedLabel(groupIndex) }}
                </span>
                <button @click.stop="clearFilter(groupIndex)"
                  class="flex-shrink-0 p-0.5 ml-1 rounded hover:text-destructive" :title="'清除选择'">
                  <X class="w-3 h-3" />
                </button>
              </div>
              <!-- 未选择状态 -->
              <span v-else class="flex-1 text-sm truncate text-muted-foreground">
                请选择
              </span>
            </div>
            <ChevronDown class="flex-shrink-0 ml-2 w-4 h-4 opacity-50 transition-transform duration-200"
              :class="openDropdown === groupIndex ? 'rotate-180' : ''" />
          </div>
        </Button>
      </div>

      <!-- 全局清空按钮 -->
      <Button v-if="hasAnySelection" variant="ghost" size="sm"
        class="flex-shrink-0 text-muted-foreground hover:text-foreground" @click="clearAllFilters">
        <X class="mr-1 w-4 h-4" />
        清空所有
      </Button>
    </div>

    <!-- 使用 Teleport 将下拉面板渲染到 body -->
    <Teleport to="body">
      <!-- 点击外部关闭下拉的遮罩层 -->
      <div v-if="openDropdown !== null" class="fixed inset-0 z-40" @click="closeDropdown"></div>

      <!-- 下拉面板 -->
      <div v-if="openDropdown !== null && visibleFilters[openDropdown]"
        class="fixed z-50 rounded-md border shadow-lg bg-popover border-border dropdown-panel" :style="dropdownStyle"
        @click.stop>
        <div class="p-3 border-b">
          <div class="flex justify-between items-center">
            <h4 class="text-sm font-medium">{{ visibleFilters[openDropdown].label }}</h4>
            <Button v-if="getSelectedCount(openDropdown) > 0" variant="ghost" size="sm" class="px-2 h-6 text-xs"
              @click="clearFilter(openDropdown)">
              清空
            </Button>
          </div>
        </div>
        <div class="overflow-y-auto p-2">
          <div class="space-y-1">
            <div v-for="option in getSearchableOptions(visibleFilters[openDropdown])" :key="option.value"
              class="flex items-center p-2 space-x-2 rounded-sm transition-colors cursor-pointer hover:bg-accent"
              @click="toggleOption(openDropdown, option.value)">
              <div class="flex justify-center items-center w-4 h-4 rounded border transition-colors" :class="isOptionSelected(openDropdown, option.value)
                ? 'bg-primary border-primary text-primary-foreground'
                : 'border-border hover:border-primary/50'">
                <Check v-if="isOptionSelected(openDropdown, option.value)" class="w-3 h-3" />
              </div>
              <span class="flex-1 text-sm font-normal">
                {{ option.name }}
              </span>
            </div>
          </div>
        </div>
        <div v-if="getSelectedCount(openDropdown) > 0" class="p-3 border-t bg-muted/30">
          <div class="text-xs text-muted-foreground">
            已选择 {{ getSelectedCount(openDropdown) }} 项
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onUnmounted, defineEmits, defineProps, nextTick } from 'vue'
import { Button } from '@/components/ui/button'
import { ChevronDown, X, Check } from 'lucide-vue-next'

// 定义筛选项的类型（基于 API 返回结构）
interface FilterOption {
  name: string  // API 返回的显示名称
  value: number | string  // API 返回的值
}

// 定义筛选组的类型（基于 API 返回结构）
interface FilterGroup {
  field_name: string  // API 字段名
  label: string       // 显示标签
  type: 'select' | 'multiselect'  // 单选或多选
  options: FilterOption[]
  is_form: string  // 是否在表单中显示 "Y" 或 "N"
  is_search: string  // 是否在搜索中显示 "Y" 或 "N"
}

// Props
const props = defineProps<{
  filters: FilterGroup[]
}>()

// Emits
const emit = defineEmits<{
  filterChange: [filters: Record<string, any>]
}>()

// 选中的值对象（以字段名为键，值为数组或单个值）
const selectedValues = ref<Record<string, any>>({})

// 过滤出可见的筛选器（is_search 为 "Y" 的）
const visibleFilters = computed(() => {
  return props.filters.filter(filter => filter.is_search === 'Y')
})

// 当前打开的下拉索引
const openDropdown = ref<number | null>(null)

// 容器和按钮引用
const containerRef = ref<HTMLElement | null>(null)
const buttonRefs = ref<Map<number, HTMLElement>>(new Map())

// 下拉框位置样式
const dropdownStyle = ref<Record<string, string>>({})

// 初始化选中值
const initializeSelectedValues = () => {
  const newSelectedValues: Record<string, any> = {}
  props.filters.forEach(filter => {
    newSelectedValues[filter.field_name] = filter.type === 'multiselect' ? [] : null
  })
  selectedValues.value = newSelectedValues
}

// 检查选项是否被选中
const isOptionSelected = (groupIndex: number, value: number | string): boolean => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return false

  const fieldValue = selectedValues.value[filter.field_name]

  if (filter.type === 'multiselect') {
    return Array.isArray(fieldValue) && fieldValue.includes(value)
  } else {
    // 单选：比较值时需要处理类型转换
    return String(fieldValue) === String(value)
  }
}

// 获取某个筛选器的选中数量
const getSelectedCount = (groupIndex: number): number => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return 0

  const fieldValue = selectedValues.value[filter.field_name]

  if (filter.type === 'multiselect') {
    return Array.isArray(fieldValue) ? fieldValue.length : 0
  } else {
    return fieldValue !== null && fieldValue !== undefined ? 1 : 0
  }
}

// 检查是否有任何选择
const hasAnySelection = computed(() => {
  return Object.values(selectedValues.value).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0
    } else {
      return value !== null && value !== undefined
    }
  })
})

// 获取可搜索的选项（由于已经在 visibleFilters 中过滤了，这里直接返回所有选项）
const getSearchableOptions = (filter: FilterGroup): FilterOption[] => {
  return filter.options
}

// 获取要显示的标签（最多3个）
const getDisplayTags = (groupIndex: number): string[] => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return []

  const fieldValue = selectedValues.value[filter.field_name]

  let selectedOptions: (number | string)[] = []
  if (filter.type === 'multiselect' && Array.isArray(fieldValue)) {
    selectedOptions = fieldValue
  } else if (filter.type === 'select' && fieldValue !== null && fieldValue !== undefined && fieldValue !== '') {
    selectedOptions = [fieldValue]
  }

  const tags = selectedOptions.map(value => {
    const option = filter.options.find(opt => String(opt.value) === String(value))
    return option?.name || String(value)
  }).filter(Boolean) // 过滤掉空值

  return tags.slice(0, 3)
}

// 获取单选时选中的标签
const getSelectedLabel = (groupIndex: number): string => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return ''

  const fieldValue = selectedValues.value[filter.field_name]

  if (filter.type === 'select' && fieldValue !== null && fieldValue !== undefined) {
    const option = filter.options.find(opt => String(opt.value) === String(fieldValue))
    return option?.name || String(fieldValue)
  }

  return ''
}

// 设置按钮引用
const setButtonRef = (el: any, index: number) => {
  if (el && el.$el) {
    // 如果是组件实例，获取其根元素
    buttonRefs.value.set(index, el.$el as HTMLElement)
  } else if (el instanceof HTMLElement) {
    // 如果是 HTML 元素
    buttonRefs.value.set(index, el)
  } else {
    buttonRefs.value.delete(index)
  }
}

// 计算下拉框位置
const calculateDropdownPosition = async (groupIndex: number) => {
  await nextTick()
  const button = buttonRefs.value.get(groupIndex)
  if (!button) return

  const rect = button.getBoundingClientRect()
  const scrollY = window.scrollY || document.documentElement.scrollTop
  const scrollX = window.scrollX || document.documentElement.scrollLeft

  // 计算下拉框的最佳位置
  const dropdownHeight = 320 // 估算的下拉框高度 (max-h-64 + padding + header + footer)
  const viewportHeight = window.innerHeight
  const spaceBelow = viewportHeight - rect.bottom
  const spaceAbove = rect.top

  let top: number

  // 如果下方空间足够，显示在按钮下方
  if (spaceBelow >= dropdownHeight || spaceBelow >= spaceAbove) {
    top = rect.bottom + scrollY + 4 // 4px 间距
  } else {
    // 否则显示在按钮上方
    top = rect.top + scrollY - dropdownHeight - 4
    if (spaceAbove < dropdownHeight) {
      top = scrollY + 10 // 顶部留出边距
    }
  }

  dropdownStyle.value = {
    left: `${rect.left + scrollX}px`,
    top: `${top}px`,
    width: `${Math.max(rect.width, 224)}px`, // 至少 224px (min-w-56)
  }
}

// 切换下拉显示
const toggleDropdown = async (groupIndex: number) => {
  if (openDropdown.value === groupIndex) {
    openDropdown.value = null
  } else {
    openDropdown.value = groupIndex
    await calculateDropdownPosition(groupIndex)
  }
}

// 关闭下拉
const closeDropdown = () => {
  openDropdown.value = null
}

// 切换选项选中状态
const toggleOption = (groupIndex: number, value: number | string) => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return

  const fieldName = filter.field_name

  console.log(`切换选项: ${filter.label}, 类型: ${filter.type}, 值: ${value}`)

  if (filter.type === 'multiselect') {
    // 多选
    if (!Array.isArray(selectedValues.value[fieldName])) {
      selectedValues.value[fieldName] = []
    }

    const currentSelections = selectedValues.value[fieldName] as (number | string)[]
    const index = currentSelections.indexOf(value)

    if (index > -1) {
      // 如果已选中，则移除
      currentSelections.splice(index, 1)
    } else {
      // 如果未选中，则添加
      currentSelections.push(value)
    }
  } else {
    // 单选
    if (String(selectedValues.value[fieldName]) === String(value)) {
      // 如果已选中，则取消选择
      selectedValues.value[fieldName] = null
    } else {
      // 选择新值
      selectedValues.value[fieldName] = value
    }

    // 单选时选择后自动关闭下拉框
    closeDropdown()
  }

  console.log(`更新后的值:`, selectedValues.value[fieldName])
  emit('filterChange', { ...selectedValues.value })
}

// 移除单个标签
const removeTag = (groupIndex: number, tagLabel: string) => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return

  const option = filter.options.find(opt => opt.name === tagLabel)
  if (option) {
    toggleOption(groupIndex, option.value)
  }
}

// 清空单个筛选器
const clearFilter = (groupIndex: number) => {
  const filter = visibleFilters.value[groupIndex]
  if (!filter) return

  const fieldName = filter.field_name

  if (filter.type === 'multiselect') {
    selectedValues.value[fieldName] = []
  } else {
    selectedValues.value[fieldName] = null
  }

  emit('filterChange', { ...selectedValues.value })
}

// 清空所有筛选器
const clearAllFilters = () => {
  // 清空所有筛选器的值，包括不可见的
  props.filters.forEach(filter => {
    if (filter.type === 'multiselect') {
      selectedValues.value[filter.field_name] = []
    } else {
      selectedValues.value[filter.field_name] = null
    }
  })
  emit('filterChange', { ...selectedValues.value })
  closeDropdown()
}

// 处理ESC键关闭下拉
const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeDropdown()
  }
}

// 处理窗口滚动和尺寸变化
const handleWindowChange = () => {
  if (openDropdown.value !== null) {
    calculateDropdownPosition(openDropdown.value)
  }
}

// 监听filters变化，重新初始化选中值
watch(() => props.filters, (newFilters, oldFilters) => {
  // 检查是否是筛选选项内容发生了变化（而不仅仅是引用变化）
  const hasContentChanged = JSON.stringify(newFilters) !== JSON.stringify(oldFilters)

  if (hasContentChanged) {
    console.log('筛选选项发生变化，重新初始化选中值')
    initializeSelectedValues()
    // 关闭当前打开的下拉框
    closeDropdown()
  }
}, { immediate: true, deep: true })

// 初始化时发送空值
watch(selectedValues, (newValues) => {
  emit('filterChange', { ...newValues })
}, { immediate: true, deep: true })

// 添加事件监听
document.addEventListener('keydown', handleKeyDown)
window.addEventListener('scroll', handleWindowChange)
window.addEventListener('resize', handleWindowChange)

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyDown)
  window.removeEventListener('scroll', handleWindowChange)
  window.removeEventListener('resize', handleWindowChange)
})
</script>

<style scoped>
/* 确保下拉面板在正确的层级 */
.relative {
  position: relative;
}

/* 水平滚动条样式优化 */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
  transition: background-color 0.2s;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* 筛选器按钮样式优化 */
.filter-button {
  transition: all 0.2s ease-in-out;
}

.filter-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 标签动画效果 */
.filter-tag {
  transition: all 0.15s ease-in-out;
}

.filter-tag:hover {
  transform: scale(1.05);
}

/* Teleport 下拉框样式 */
.dropdown-panel {
  min-width: 224px;
  /* min-w-56 */
  transition: opacity 0.15s ease-in-out, transform 0.15s ease-in-out;
  transform-origin: top;
  animation: dropdown-enter 0.15s ease-out;
}

@keyframes dropdown-enter {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 提升下拉框的最大高度样式 */
.dropdown-panel .overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) transparent;
}

.dropdown-panel .overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.dropdown-panel .overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-panel .overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .min-w-40 {
    min-width: 150px;
  }

  .max-w-80 {
    max-width: 200px;
  }

  .dropdown-panel {
    min-width: 200px;
    max-width: 90vw;
  }
}
</style>
