<template>
  <div class="p-4 space-y-3 border-b">
    <h5 class="text-sm font-medium text-muted-foreground">文件属性</h5>

    <div class="space-y-2">
      <!-- 文件扩展名 -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">文件类型</span>
        <span class="text-sm font-medium">{{ getFileExtension(item.name) || '无扩展名' }}</span>
      </div>

      <!-- MIME类型 -->
      <div v-if="item.mimeType" class="flex justify-between">
        <span class="text-sm text-muted-foreground">MIME类型</span>
        <span class="font-mono text-sm font-medium">{{ item.mimeType }}</span>
      </div>

      <!-- 文件版本 -->
      <div v-if="item.version" class="flex justify-between">
        <span class="text-sm text-muted-foreground">版本</span>
        <span class="text-sm font-medium">{{ item.version }}</span>
      </div>

      <!-- 编码格式 -->
      <div v-if="item.encoding" class="flex justify-between">
        <span class="text-sm text-muted-foreground">编码</span>
        <span class="text-sm font-medium">{{ item.encoding }}</span>
      </div>

      <!-- 校验值 -->
      <div v-if="item.checksum" class="flex justify-between">
        <span class="text-sm text-muted-foreground">校验值</span>
        <span class="font-mono text-sm font-medium break-all">{{ item.checksum }}</span>
      </div>

      <!-- 媒体文件特有属性 -->
      <template v-if="isMediaFile(item.type)">
        <!-- 分辨率 -->
        <div v-if="item.resolution" class="flex justify-between">
          <span class="text-sm text-muted-foreground">分辨率</span>
          <span class="text-sm font-medium">{{ item.resolution }}</span>
        </div>

        <!-- 时长 -->
        <div v-if="item.duration" class="flex justify-between">
          <span class="text-sm text-muted-foreground">时长</span>
          <span class="text-sm font-medium">{{ item.duration }}</span>
        </div>

        <!-- 比特率 -->
        <div v-if="item.bitrate" class="flex justify-between">
          <span class="text-sm text-muted-foreground">比特率</span>
          <span class="text-sm font-medium">{{ item.bitrate }}</span>
        </div>
      </template>

      <!-- 图片文件特有属性 -->
      <template v-if="isImageFile(item.type)">
        <!-- 尺寸 -->
        <div v-if="item.dimensions" class="flex justify-between">
          <span class="text-sm text-muted-foreground">尺寸</span>
          <span class="text-sm font-medium">{{ item.dimensions }}</span>
        </div>

        <!-- 色彩空间 -->
        <div v-if="item.colorSpace" class="flex justify-between">
          <span class="text-sm text-muted-foreground">色彩空间</span>
          <span class="text-sm font-medium">{{ item.colorSpace }}</span>
        </div>
      </template>

      <!-- 文档文件特有属性 -->
      <template v-if="isDocumentFile(item.type)">
        <!-- 页数 -->
        <div v-if="item.pageCount" class="flex justify-between">
          <span class="text-sm text-muted-foreground">页数</span>
          <span class="text-sm font-medium">{{ item.pageCount }} 页</span>
        </div>

        <!-- 作者 -->
        <div v-if="item.author" class="flex justify-between">
          <span class="text-sm text-muted-foreground">作者</span>
          <span class="text-sm font-medium">{{ item.author }}</span>
        </div>

        <!-- 标题 -->
        <div v-if="item.title && item.title !== item.name" class="flex justify-between">
          <span class="text-sm text-muted-foreground">标题</span>
          <span class="text-sm font-medium">{{ item.title }}</span>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FileItemType } from '@/types/files'

// Props
defineProps<{
  item: FileItemType
}>()

// 获取文件扩展名
const getFileExtension = (filename: string): string => {
  const lastDot = filename.lastIndexOf('.')
  return lastDot > 0 ? filename.substring(lastDot + 1).toUpperCase() : ''
}

// 判断是否为媒体文件
const isMediaFile = (type: string): boolean => {
  return ['video', 'audio'].includes(type)
}

// 判断是否为图片文件
const isImageFile = (type: string): boolean => {
  return type === 'image'
}

// 判断是否为文档文件
const isDocumentFile = (type: string): boolean => {
  return ['document', 'pdf', 'text'].includes(type)
}
</script>