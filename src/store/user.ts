import { defineStore } from "pinia";
import { ref, computed } from "vue";
import { toast } from "vue-sonner";
import { useRouter } from "vue-router";
import api from "@/api";
import type { UserInfo, MenuItem } from "@/types/user";
import { isLoggingOutState } from "@/composables/useAuth";

/**
 * 用户信息状态管理 Store
 */
export const useUserStore = defineStore("user", () => {
  // ==================== 路由实例 ====================

  // 获取路由实例的方法
  const getRouter = () => {
    try {
      return useRouter();
    } catch (error) {
      console.warn("无法在当前上下文中获取router实例，使用强制重定向");
      return null;
    }
  };

  // ==================== HTTP认证失败监听 ====================

  // 监听HTTP层的认证失败事件
  const handleHttpAuthFailure = (event: CustomEvent) => {
    const { source, message } = event.detail;
    console.log(`🚫 收到HTTP认证失败事件 (${source}):`, message);

    // 如果已经设置了认证失败标记，避免重复处理
    if (authFailureFlag) {
      console.log("⚠️ 认证失败已在处理中，跳过重复处理");
      return;
    }

    // 设置认证失败标记，防止无限循环
    authFailureFlag = true;

    // 通过事件通知应用层清除认证状态
    if (typeof window !== "undefined") {
      window.dispatchEvent(
        new CustomEvent("clear-auth-state", {
          detail: { source: "http-auth-failure" },
        })
      );
    }

    // 调用统一的认证失败处理
    handleAuthFailure("HTTP请求认证失败", true);
  };

  // 初始化HTTP认证失败监听器
  if (typeof window !== "undefined") {
    window.addEventListener("http-auth-failure", handleHttpAuthFailure as EventListener);
  }

  // ==================== 状态 ====================

  // 用户基本信息
  const userInfo = ref<UserInfo | null>(null);

  // 菜单配置
  const menu = ref<MenuItem[]>([]);

  // 认证token
  const token = ref<string | null>(null);

  // 加载状态
  const isLoading = ref(false);
  const isLoaded = ref(false);

  // 错误状态
  const error = ref<string | null>(null);

  // Promise缓存，防止重复请求
  let fetchPromise: Promise<any> | null = null;

  // 认证失败标记，防止无限循环
  let authFailureFlag = false;

  // ==================== 计算属性 ====================

  // 用户名显示（用于导航栏）
  const displayName = computed(() => {
    return userInfo.value?.username || "用户";
  });

  // 用户ID
  const userId = computed(() => {
    return userInfo.value?.user_id || null;
  });

  // 是否已登录（基于用户信息是否存在）
  const isUserInfoLoaded = computed(() => {
    return !!userInfo.value;
  });

  // 权限管理菜单URL（用于iframe嵌入）
  const permissionsUrl = computed(() => {
    const permissionsMenu = menu.value.find((item) => item.code === "permissions");
    return permissionsMenu?.url || null;
  });

  // ==================== Actions ====================

  /**
   * 处理登录验证失败的统一逻辑
   */
  const handleAuthFailure = (errorMessage: string, isNetworkError = false) => {
    // 如果正在退出登录，不处理认证失败，避免重定向循环
    if (isLoggingOutState()) {
      console.log("🚪 正在退出登录，跳过认证失败处理");
      return;
    }

    // 设置错误状态
    error.value = errorMessage;

    // 显示错误提示
    const description = isNetworkError ? "无法验证用户身份，请重新登录" : "请检查您的登录状态或重新登录";

    toast.error(`登录验证失败: ${errorMessage}`, {
      description,
      duration: 5000,
    });

    // 清除认证状态和令牌
    clearUserInfo();
    if (typeof localStorage !== "undefined") {
      localStorage.removeItem("auth_token");
    }

    // 延迟重定向到登录页面，确保错误提示能够显示
    // 移除window.location.href fallback，统一使用Vue Router
    setTimeout(() => {
      if (typeof window !== "undefined") {
        const router = getRouter();
        if (router) {
          try {
            router.push("/login");
            console.log("🔄 使用Vue Router重定向到登录页");
          } catch (error) {
            console.error("❌ Vue Router重定向失败:", error);
            // 在Electron环境中，不使用window.location.href，而是发送事件
            window.dispatchEvent(
              new CustomEvent("force-navigate-to-login", {
                detail: { reason: "router-push-failed" },
              })
            );
          }
        } else {
          console.warn("⚠️ 无法获取Router实例，发送强制导航事件");
          // 发送事件而不是直接使用window.location.href
          window.dispatchEvent(
            new CustomEvent("force-navigate-to-login", {
              detail: { reason: "no-router-instance" },
            })
          );
        }
      }
    }, 1000);
  };

  /**
   * 获取用户信息和菜单配置
   */
  const fetchUserInfo = async (force = false) => {
    // 如果认证已失败且不是强制刷新，直接拒绝请求防止无限循环
    if (authFailureFlag && !force) {
      throw new Error("认证失败，请重新登录");
    }

    // 如果已经加载过且不强制刷新，直接返回
    if (isLoaded.value && !force) {
      return { userInfo: userInfo.value, menu: menu.value };
    }

    // 如果正在加载中且不强制刷新，返回缓存的Promise
    if (isLoading.value && !force && fetchPromise) {
      return fetchPromise;
    }

    // 检查是否有无效的缓存Promise
    if (isLoading.value && fetchPromise === null) {
      isLoading.value = false;
    }

    isLoading.value = true;
    error.value = null;

    // 创建并缓存Promise
    fetchPromise = (async () => {
      try {
        const res = await api.common.getUserInfo();

        // 严格验证API响应
        console.log("🔍 getUserInfo API响应:", {
          code: res.code,
          msg: res.msg,
          hasData: !!res.data,
          data: res.data,
        });

        if (res.code === 0 && res.data) {
          // 存储用户信息
          userInfo.value = res.data.user_info;
          menu.value = res.data.menu || []; // 确保menu是数组
          isLoaded.value = true;

          return { userInfo: userInfo.value, menu: menu.value };
        } else {
          // API返回失败状态
          const errorMessage = res.msg || "获取用户信息失败";

          // 对于401错误，由HTTP拦截器统一处理，避免重复处理
          if (res.code !== 401) {
            authFailureFlag = true;
            handleAuthFailure(errorMessage, false);
          }

          throw new Error(errorMessage);
        }
      } catch (err) {
        // 检查是否是API业务逻辑错误（我们主动抛出的）
        const isApiBusinessError = err instanceof Error && err.message.includes("获取用户信息失败");

        if (!isApiBusinessError) {
          // 网络错误或其他异常
          const errorMessage = err instanceof Error ? err.message : "网络请求失败";
          authFailureFlag = true;
          handleAuthFailure(errorMessage, true);
        }

        // 重新抛出错误，阻止登录
        throw err;
      } finally {
        isLoading.value = false;
        fetchPromise = null; // 清除缓存
      }
    })();

    return fetchPromise;
  };

  /**
   * 清除用户信息（用于退出登录）
   */
  const clearUserInfo = () => {
    userInfo.value = null;
    menu.value = [];
    token.value = null;
    isLoaded.value = false;
    isLoading.value = false;
    error.value = null;
    fetchPromise = null; // 清除缓存的Promise
    authFailureFlag = false; // 重置认证失败标记
  };

  /**
   * 设置用户信息（用于直接设置，比如从其他地方获取的数据）
   */
  const setUserInfo = (newUserInfo: UserInfo, newMenu: MenuItem[]) => {
    userInfo.value = newUserInfo;
    menu.value = newMenu;
    isLoaded.value = true;
    error.value = null;
    console.log("✅ 用户信息已设置:", {
      username: newUserInfo.username,
      menuCount: newMenu.length,
    });
  };

  /**
   * 设置token
   */
  const setToken = (newToken: string | null) => {
    token.value = newToken;
    if (newToken) {
      localStorage.setItem("auth_token", newToken);
      console.log("✅ Token已设置到用户store");
    } else {
      localStorage.removeItem("auth_token");
      console.log("🧹 Token已从用户store清除");
    }
  };

  /**
   * 获取token
   */
  const getToken = (): string | null => {
    if (!token.value) {
      // 尝试从localStorage恢复token
      const storedToken = localStorage.getItem("auth_token");
      if (storedToken) {
        token.value = storedToken;
      }
    }
    return token.value;
  };

  /**
   * 重置错误状态
   */
  const clearError = () => {
    error.value = null;
  };

  /**
   * 强制重新获取用户信息（忽略缓存）
   */
  const forceRefreshUserInfo = async () => {
    authFailureFlag = false; // 强制刷新时重置认证失败标记
    return fetchUserInfo(true);
  };

  /**
   * 清理资源（移除事件监听器）
   */
  const cleanup = () => {
    if (typeof window !== "undefined") {
      window.removeEventListener("http-auth-failure", handleHttpAuthFailure as EventListener);
    }
  };

  // ==================== 返回值 ====================

  return {
    // 状态
    userInfo,
    menu,
    token,
    isLoading,
    isLoaded,
    error,

    // 计算属性
    displayName,
    userId,
    isUserInfoLoaded,
    permissionsUrl,

    // Actions
    fetchUserInfo,
    clearUserInfo,
    setUserInfo,
    setToken,
    getToken,
    clearError,
    forceRefreshUserInfo,
    cleanup,
  };
});
