# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-electron
release
release-test
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 构建输出
build/
out/

# Electron 特有文件
app/
packages/
*.blockmap

# 操作系统生成的文件
Thumbs.db
ehthumbs.db
Desktop.ini

# IDE 相关
.vscode/
.idea/
*.swp
*.swo

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 代码覆盖率目录（如 istanbul 等工具使用）
coverage/

# nyc 测试覆盖率
.nyc_output

# node-waf 配置
.lock-wscript

# 编译的二进制插件 (https://nodejs.org/api/addons.html)
build/Release

# 依赖目录
jspm_packages/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 可选的 REPL 历史
.node_repl_history

# 'npm pack' 的输出
*.tgz

# Yarn 完整性文件
.yarn-integrity

# parcel-bundler 缓存 (https://parceljs.org/)
.cache
.parcel-cache

# next.js 构建输出
.next

# nuxt.js 构建输出
.nuxt

# vuepress 构建输出
.vuepress/dist

# Serverless 目录
.serverless

# FuseBox 缓存
.fusebox/

# DynamoDB 本地文件
.dynamodb/