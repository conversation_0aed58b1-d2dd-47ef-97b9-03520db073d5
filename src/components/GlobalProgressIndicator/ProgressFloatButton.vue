<template>
  <div class="relative">
    <Button @click="$emit('toggle')"
      class="flex items-center justify-center w-12 h-12 p-0 border rounded-full shadow-lg bg-background hover:bg-accent border-border">
      <!-- 图标 (只在无活跃任务时显示) -->
      <component v-if="!hasAnyActiveTasks" :is="UploadIcon" class="w-5 h-5 text-foreground" />

      <!-- 进度圆环 (当有活跃任务时) -->
      <svg v-if="hasAnyActiveTasks" class="w-10 h-10 -rotate-90" viewBox="0 0 36 36">
        <!-- 背景圆环 -->
        <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none"
          stroke="hsl(var(--muted))" stroke-width="3" />
        <!-- 进度圆环 -->
        <path d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" fill="none"
          stroke="hsl(var(--primary))" stroke-width="3" :stroke-dasharray="`${combinedProgress} 100`"
          class="transition-all duration-500" stroke-linecap="round" />
      </svg>

      <!-- 任务数量 Badge (右上角) -->
      <div v-if="totalActiveCount > 0"
        class="absolute top-0 right-0 flex items-center justify-center w-4 h-4 text-xs font-medium text-white transform translate-x-1 -translate-y-1 bg-red-500 rounded-full">
        {{ totalActiveCount > 9 ? '9+' : totalActiveCount }}
      </div>

      <!-- 错误标识 (右上角) -->
      <div v-else-if="errorTasks.length > 0"
        class="absolute top-0 right-0 flex items-center justify-center w-4 h-4 transform translate-x-1 -translate-y-1 bg-red-500 rounded-full">
        <AlertCircleIcon class="w-2.5 h-2.5 text-white" />
      </div>
    </Button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Upload as UploadIcon,
  AlertCircle as AlertCircleIcon
} from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { useTusUpload } from '@/components/Upload/composables/useTusUpload'
import type { ProgressTask } from '@/composables/useGlobalProgress'

interface Props {
  activeTasks: ProgressTask[]
  uploadTasks: ProgressTask[]
  errorTasks: ProgressTask[]
  hasActiveTasks: boolean
  overallProgress: number
}

const props = defineProps<Props>()
defineEmits<{
  toggle: []
}>()

// 集成 TUS 上传功能
const tusUpload = useTusUpload()

// 获取批量任务
const activeBatchTasks = computed(() =>
  Array.from(tusUpload.batchTasks.value.values()).filter(task =>
    ['pending', 'uploading'].includes(task.status)
  )
)

// 计算总活跃任务数（包括批量任务）
const totalActiveCount = computed(() => {
  return props.activeTasks.length + activeBatchTasks.value.length
})

// 是否有任何活跃任务
const hasAnyActiveTasks = computed(() => {
  return props.hasActiveTasks || activeBatchTasks.value.length > 0
})

// 计算组合进度
const combinedProgress = computed(() => {
  const regularTasks = props.activeTasks
  const batchTasksActive = activeBatchTasks.value

  const allActiveTasks = [...regularTasks, ...batchTasksActive]
  if (allActiveTasks.length === 0) return 0

  const totalProgress = allActiveTasks.reduce((sum, task) => sum + task.progress, 0)
  return Math.round(totalProgress / allActiveTasks.length)
})
</script>