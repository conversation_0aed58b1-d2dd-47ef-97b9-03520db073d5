import { ref, computed } from "vue";
import type { ItemType } from "@/types/files";
import { formatFileSize } from "@/lib/upload-utils";
import { toast } from "vue-sonner";

// 下载信息接口
export interface DownloadInfo {
  name: string;
  count?: number;
  size?: string;
}

// 路径选择结果接口
export interface PathSelectionResult {
  path: string;
  remember: boolean;
  cancelled: boolean;
}

/**
 * 下载路径选择 composable
 */
export function useDownloadPath() {
  // 状态
  const showPathDialog = ref(false);
  const currentDownloadInfo = ref<DownloadInfo | null>(null);
  const isSelecting = ref(false);

  // 路径选择结果的 Promise resolver
  let pathSelectionResolver: ((result: PathSelectionResult) => void) | null = null;

  /**
   * 计算下载信息
   */
  const calculateDownloadInfo = (items: ItemType | ItemType[]): DownloadInfo => {
    const itemsArray = Array.isArray(items) ? items : [items];

    if (itemsArray.length === 1) {
      const item = itemsArray[0];
      let size: string | undefined;

      if (item.type === "folder") {
        size = undefined; // 文件夹不显示大小
      } else {
        const fileItem = item as any; // 使用 any 来访问 size_human
        // 优先使用 size_human，回退到格式化的 size
        if (fileItem.size_human && fileItem.size_human.trim() !== "") {
          size = fileItem.size_human;
        } else if (typeof fileItem.size === "number" && fileItem.size > 0) {
          size = formatFileSize(fileItem.size);
        }
      }

      return {
        name: item.name,
        size,
      };
    } else {
      // 批量下载时，计算总大小
      let totalSize = 0;
      let hasValidSize = false;

      itemsArray.forEach((item) => {
        if (item.type !== "folder") {
          const fileItem = item as any;
          if (typeof fileItem.size === "number" && fileItem.size > 0) {
            totalSize += fileItem.size;
            hasValidSize = true;
          }
        }
      });

      return {
        name: `${itemsArray.length} 个项目`,
        count: itemsArray.length,
        size: hasValidSize ? formatFileSize(totalSize) : undefined,
      };
    }
  };

  /**
   * 显示路径选择对话框
   */
  const showPathSelection = async (items: ItemType | ItemType[]): Promise<PathSelectionResult> => {
    if (isSelecting.value) {
      throw new Error("路径选择已在进行中");
    }

    return new Promise((resolve) => {
      isSelecting.value = true;
      currentDownloadInfo.value = calculateDownloadInfo(items);
      pathSelectionResolver = resolve;
      showPathDialog.value = true;
    });
  };

  /**
   * 处理路径选择确认
   */
  const handlePathConfirm = (path: string, remember: boolean) => {
    if (pathSelectionResolver) {
      pathSelectionResolver({
        path,
        remember,
        cancelled: false,
      });
      resetSelection();
    }
  };

  /**
   * 处理路径选择取消
   */
  const handlePathCancel = () => {
    if (pathSelectionResolver) {
      pathSelectionResolver({
        path: "",
        remember: false,
        cancelled: true,
      });
      resetSelection();
    }
  };

  /**
   * 重置选择状态
   */
  const resetSelection = () => {
    showPathDialog.value = false;
    currentDownloadInfo.value = null;
    pathSelectionResolver = null;
    isSelecting.value = false;
  };

  /**
   * 获取默认下载路径
   */
  const getDefaultDownloadPath = async (): Promise<string> => {
    try {
      const api = (window as any).electronAPI;
      if (api?.download?.getDefaultDownloadPath) {
        const result = await api.download.getDefaultDownloadPath();
        if (result.success && result.data?.path) {
          return result.data.path;
        }
      }
    } catch (error) {
      console.warn("获取默认下载路径失败:", error);
    }
    return "";
  };

  /**
   * 获取记住的下载路径
   */
  const getRememberedPath = (): string => {
    return localStorage.getItem("download-remembered-path") || "";
  };

  /**
   * 保存记住的路径
   */
  const saveRememberedPath = (path: string) => {
    if (path) {
      localStorage.setItem("download-remembered-path", path);
    }
  };

  /**
   * 清除记住的路径
   */
  const clearRememberedPath = () => {
    localStorage.removeItem("download-remembered-path");
  };

  /**
   * 获取推荐的下载路径
   */
  const getRecommendedPath = async (): Promise<string> => {
    // 优先使用记住的路径
    const rememberedPath = getRememberedPath();
    if (rememberedPath) {
      return rememberedPath;
    }

    // 否则使用默认路径
    return await getDefaultDownloadPath();
  };

  /**
   * 直接选择路径（不显示对话框）
   */
  const selectPathDirectly = async (defaultPath?: string): Promise<string | null> => {
    try {
      const api = (window as any).electronAPI;
      if (!api?.download?.showSelectFolderDialog) {
        toast.error("文件夹选择功能不可用");
        return null;
      }

      const result = await api.download.showSelectFolderDialog(defaultPath);

      if (result.success && result.data?.path) {
        return result.data.path;
      } else if (result.error && !result.error.includes("用户取消")) {
        toast.error(`选择文件夹失败: ${result.error}`);
      }

      return null;
    } catch (error) {
      console.error("选择文件夹失败:", error);
      toast.error("选择文件夹时发生错误");
      return null;
    }
  };

  /**
   * 验证路径是否有效
   */
  const validatePath = (path: string): boolean => {
    if (!path || path.trim() === "") {
      return false;
    }

    // 基本路径格式验证
    try {
      // 简单的路径格式检查
      return path.length > 0 && !path.includes("<") && !path.includes(">");
    } catch {
      return false;
    }
  };

  // 计算属性
  const isDialogOpen = computed(() => showPathDialog.value);
  const downloadInfo = computed(() => currentDownloadInfo.value);

  return {
    // 状态
    isSelecting,
    isDialogOpen,
    downloadInfo,
    showPathDialog,

    // 方法
    showPathSelection,
    handlePathConfirm,
    handlePathCancel,
    getDefaultDownloadPath,
    getRememberedPath,
    saveRememberedPath,
    clearRememberedPath,
    getRecommendedPath,
    selectPathDirectly,
    validatePath,
    resetSelection,
  };
}
