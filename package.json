{"name": "cloud-drive", "version": "1.0.0", "description": "A modern cloud drive application built with Electron, Vue, and TypeScript", "main": "dist-electron/main.js", "author": "<PERSON><PERSON><PERSON>", "homepage": "./", "packageManager": "pnpm@10.11.1", "scripts": {"dev": "vite --mode development", "dev:electron": "concurrently \"pnpm dev\" \"pnpm electron\"", "build": "vue-tsc --noEmit && vite build && electron-builder", "build:web": "vue-tsc --noEmit && vite build", "build:electron": "electron-builder", "build:mac": "vue-tsc --noEmit && vite build && cross-env NODE_ENV=production electron-builder --mac --config electron-builder.yml", "build:win": "vue-tsc --noEmit && vite build && cross-env NODE_ENV=production electron-builder --win --config electron-builder.yml", "build:test:mac": "vue-tsc --noEmit && vite build --mode test && cross-env NODE_ENV=test electron-builder --mac --config electron-builder.test.yml", "build:test:win": "vue-tsc --noEmit && vite build --mode test && cross-env NODE_ENV=test electron-builder --win --config electron-builder.test.yml", "electron": "wait-on http://localhost:5173 && cross-env NODE_ENV=development electron --inspect=9229 --sourcemap .", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "lint": "vue-tsc --noEmit", "clean": "rm -rf dist dist-electron release"}, "dependencies": {"7zip-bin": "^5.2.0", "@radix-icons/vue": "^1.0.0", "@tanstack/vue-table": "^8.21.3", "@vueuse/core": "^10.7.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "dotenv": "^16.5.0", "electron-store": "^8.1.0", "electron-updater": "^6.1.7", "lucide-vue-next": "^0.292.0", "node-7z": "^3.0.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.3.0", "radix-vue": "^1.4.0", "reka-ui": "^2.3.0", "shadcn-vue": "^2.2.0", "streamsaver": "^2.0.6", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "tus-js-client": "^4.3.1", "vue": "^3.4.0", "vue-router": "^4.2.5", "vue-sonner": "^2.0.0"}, "devDependencies": {"@types/node": "^20.10.0", "@vitejs/plugin-vue": "^4.5.2", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.0.0", "electron-builder": "^24.9.1", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.4.0", "vite": "^5.0.10", "vite-plugin-electron": "^0.28.0", "vite-plugin-electron-renderer": "^0.14.5", "vue-tsc": "^2.0.0", "wait-on": "^7.2.0"}}