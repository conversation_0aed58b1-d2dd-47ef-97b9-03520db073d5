<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-2xl max-h-[90vh] flex flex-col">
      <DialogHeader class="flex-shrink-0">
        <DialogTitle>编辑</DialogTitle>
        <DialogDescription>
          编辑 "{{ categoryData?.category_name }}" 类别下的标签列表
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="flex flex-col flex-1 min-h-0">

        <div class="mb-2 space-y-2">
          <label class="text-sm font-medium">标签列表</label>
        </div>

        <!-- 可滚动的标签列表区域 -->
        <div class="flex flex-col flex-1 min-h-0">
          <div ref="tagListContainer" class="overflow-y-auto flex-1 py-2 pr-2 pl-1 space-y-2 tag-list-container"
            :style="{ maxHeight: maxTagListHeight }">
            <div v-for="(_tag, index) in formData.tag_values" :key="index" class="flex gap-2 items-center">
              <Input v-model="formData.tag_values[index].name" placeholder="请输入标签名称" class="flex-1" />
              <Button type="button" variant="destructive" size="sm" @click="removeTag(index)"
                :disabled="formData.tag_values.length <= 1">
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div class="flex-shrink-0 pt-4">
            <Button type="button" variant="default" size="sm" @click="addTag"
              class="w-full text-white bg-green-500 border-green-500 hover:bg-green-400 hover:border-green-400">
              <Plus class="mr-2 w-4 h-4" />
              添加
            </Button>
          </div>
        </div>

        <DialogFooter class="flex-shrink-0 mt-4">
          <Button type="button" variant="outline" @click="handleCancel">
            取消
          </Button>
          <Button type="submit" :disabled="loading || !isValid">
            <RefreshCw v-if="loading" class="mr-2 w-4 h-4 animate-spin" />
            保存
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { RefreshCw, Plus, Trash2 } from 'lucide-vue-next'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import type { TagCategory, EditTagParams, TagValue } from '@/api/services/tags'

interface Props {
  open: boolean
  categoryData?: TagCategory | null
}

interface Emits {
  'update:open': [value: boolean]
  'submit': [data: EditTagParams]
}

const props = withDefaults(defineProps<Props>(), {
  categoryData: null,
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  tag_values: [] as TagValue[],
})

// 加载状态
const loading = ref(false)

// 视窗高度
const windowHeight = ref(window.innerHeight)

// 滚动容器的 ref
const tagListContainer = ref<HTMLElement | null>(null)

// 计算属性
const isValid = computed(() => {
  return formData.value.tag_values.length > 0 &&
    formData.value.tag_values.every(tag => tag.name.trim().length > 0)
})

// 计算标签列表的最大高度（视窗高度的70%减去其他固定元素的高度）
const maxTagListHeight = computed(() => {
  const maxDialogHeight = windowHeight.value * 0.9 // 弹窗最大高度为视窗的90%
  const fixedElementsHeight = 300 // 预估的固定元素高度（标题、类别名称、按钮等）
  const maxTagListHeight = Math.max(200, maxDialogHeight * 0.7 - fixedElementsHeight) // 最小200px
  return `${maxTagListHeight}px`
})

// 监听窗口大小变化
const handleResize = () => {
  windowHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// 重置表单
const resetForm = () => {
  formData.value = {
    tag_values: [],
  }
}

// 监听类别数据变化
watch(
  () => props.categoryData,
  (newData) => {
    if (newData) {
      formData.value = {
        tag_values: newData.tags.map(tag => ({
          name: tag.item_value,
          id: tag.id
        })),
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

// 监听对话框打开状态
watch(
  () => props.open,
  (isOpen) => {
    if (!isOpen) {
      resetForm()
    }
  }
)



// 添加标签
const addTag = async () => {
  // 添加新标签
  formData.value.tag_values.push({
    name: '',
    id: -1 // 新增标签使用 -1
  })

  // 等待 DOM 更新后聚焦到新增的输入框
  await nextTick()

  try {
    // 使用更简单的方法：直接查找最后一个输入框
    if (tagListContainer.value) {
      const allInputs = tagListContainer.value.querySelectorAll('input')
      const lastInput = allInputs[allInputs.length - 1]

      if (lastInput && typeof lastInput.focus === 'function') {
        // 聚焦到最后一个（新增的）输入框
        lastInput.focus()

        // 滚动到底部以确保新增的输入框可见
        tagListContainer.value.scrollTop = tagListContainer.value.scrollHeight
      }
    }
  } catch (error) {
    console.warn('聚焦到新增标签输入框时出错:', error)
  }
}

// 移除标签
const removeTag = (index: number) => {
  if (formData.value.tag_values.length > 1) {
    formData.value.tag_values.splice(index, 1)
  }
}

// 处理提交
const handleSubmit = async () => {
  if (!props.categoryData || !isValid.value) return

  try {
    loading.value = true

    // 过滤掉空的标签并保持原有结构
    const filteredTags = formData.value.tag_values
      .map(tag => ({
        name: tag.name.trim(),
        id: tag.id
      }))
      .filter(tag => tag.name.length > 0)

    const editData: EditTagParams = {
      category_id: String(props.categoryData.category_id), // 转换为字符串格式
      tag_values: filteredTags,
    }

    emit('submit', editData)
  } catch (error) {
    // 错误处理由父组件负责
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}
</script>

<style scoped>
/* 自定义滚动条样式 */
.tag-list-container::-webkit-scrollbar {
  width: 6px;
}

.tag-list-container::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 3px;
}

.tag-list-container::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

.tag-list-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Firefox 滚动条样式 */
.tag-list-container {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
}

/* 滚动区域的平滑滚动 */
.tag-list-container {
  scroll-behavior: smooth;
}

/* 确保滚动容器有足够的内边距 */
.tag-list-container {
  padding-bottom: 8px;
}
</style>
