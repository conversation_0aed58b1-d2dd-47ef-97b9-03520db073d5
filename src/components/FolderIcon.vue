<template>
  <div class="flex items-center gap-2">
    <component :is="folderConfig.icon" class="w-5 h-5" :class="folderConfig.iconColor" />
    <span class="text-sm font-medium">{{ folderConfig.name }}</span>
    <span v-if="showCount && folderConfig.count" class="text-xs text-muted-foreground">
      ({{ folderConfig.count }})
    </span>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useFolderConfig } from '@/composables/useFolderConfig'

interface Props {
  /** 文件夹类型 */
  folderType: string
  /** 是否显示文件数量 */
  showCount?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showCount: false
})

const { getFolderConfig } = useFolderConfig()

// 获取文件夹配置
const folderConfig = computed(() => getFolderConfig(props.folderType))
</script>