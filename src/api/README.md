# API 层使用说明

## 📋 概述

本项目使用 Axios 作为 HTTP 请求库，并在 `src/api` 目录中进行了完整的封装。支持请求/响应拦截、错误处理、类型安全等特性。

## 🏗️ 目录结构

```
src/api/
├── index.ts              # API 统一入口
├── http.ts               # HTTP 客户端封装
├── services/             # API 服务层
│   ├── auth.ts          # 认证相关 API
│   └── files.ts         # 文件管理 API
└── README.md            # 本文档
```

## 🔧 配置文件

### 环境变量配置

项目支持多环境配置，环境变量文件包括：

- `.env.local` - 本地开发配置
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置

### 主要环境变量

```env
# API 配置
VITE_API_BASE_URL=http://localhost:3000/api
VITE_API_TIMEOUT=10000

# TUS 上传配置
VITE_TUS_ENDPOINT=https://tusd.tusdemo.net/files/
VITE_TUS_CHUNK_SIZE=5242880
VITE_TUS_PARALLEL_UPLOADS=3

# 开发配置
VITE_ENABLE_DEBUG=false
VITE_ENABLE_MOCK=false
```

## 📖 使用方法

### 1. 基础用法

```typescript
import { api } from '@/api';

// 登录
const loginResponse = await api.auth.login({
  username: '<EMAIL>',
  password: 'password123'
});

// 获取文件列表
const filesResponse = await api.files.getFileList({
  page: 1,
  pageSize: 20
});
```

### 2. 直接使用 HTTP 客户端

```typescript
import { request } from '@/api';

// GET 请求
const data = await request.get('/custom-endpoint');

// POST 请求
const result = await request.post('/custom-endpoint', {
  key: 'value'
});
```

### 3. 自定义 HTTP 客户端

```typescript
import { HttpClient } from '@/api';

const customClient = new HttpClient({
  baseURL: 'https://api.example.com',
  timeout: 5000
});

const response = await customClient.get('/data');
```

## 🔐 认证处理

### Token 管理

HTTP 客户端会自动从 localStorage 中读取 `auth_token` 并添加到请求头：

```typescript
// 设置 token
localStorage.setItem('auth_token', 'your-jwt-token');

// 清除 token
localStorage.removeItem('auth_token');
```

### 认证失效处理

当收到 401 响应时，系统会自动：
1. 清除本地存储的 token
2. 在控制台输出警告信息
3. 可以扩展跳转到登录页面的逻辑

## 📝 API 响应格式

### 标准响应格式

```typescript
interface ApiResponse<T = any> {
  code: number;        // 业务状态码
  message: string;     // 响应消息
  data: T;            // 响应数据
  success: boolean;    // 是否成功
  timestamp: number;   // 时间戳
}
```

### 分页响应格式

```typescript
interface PaginatedResponse<T = any> {
  list: T[];          // 数据列表
  total: number;      // 总数量
  page: number;       // 当前页码
  pageSize: number;   // 每页大小
  totalPages: number; // 总页数
}
```

### 错误响应格式

```typescript
interface ErrorResponse {
  code: number;       // 错误码
  message: string;    // 错误信息
  details?: any;      // 错误详情
}
```

## 🛠️ 请求拦截器

### 请求拦截功能

- **自动添加认证头**: Bearer Token
- **请求追踪**: 添加唯一请求 ID
- **调试日志**: 开发环境下打印请求信息

### 响应拦截功能

- **业务状态检查**: 检查 `success` 字段
- **错误统一处理**: HTTP 状态码错误处理
- **调试日志**: 开发环境下打印响应信息

## 🎯 服务层 API

### 认证服务 (`authApi`)

```typescript
// 用户登录
await api.auth.login({ username, password });

// 获取当前用户信息
await api.auth.getCurrentUser();

// 刷新 token
await api.auth.refreshToken(refreshToken);

// 更新用户资料
await api.auth.updateProfile({ email: '<EMAIL>' });
```

### 文件服务 (`filesApi`)

```typescript
// 获取文件列表
await api.files.getFileList({ parentId: 'folder-id' });

// 上传文件
await api.files.uploadFile(file, parentId, (progress) => {
  console.log(`上传进度: ${progress}%`);
});

// 创建文件夹
await api.files.createFolder({ name: '新文件夹', parentId });

// 删除文件
await api.files.deleteFiles(['file-id-1', 'file-id-2']);

// 分享文件
await api.files.shareFiles({
  fileIds: ['file-id'],
  permissions: ['read'],
  expiresAt: '2024-12-31'
});
```

## 🚨 错误处理

### 全局错误处理

```typescript
try {
  const data = await api.files.getFileList();
} catch (error: ErrorResponse) {
  console.error('请求失败:', error.message);
  
  switch (error.code) {
    case 401:
      // 处理认证失败
      break;
    case 403:
      // 处理权限不足
      break;
    case 404:
      // 处理资源不存在
      break;
    default:
      // 处理其他错误
      break;
  }
}
```

### 网络错误处理

```typescript
try {
  const data = await api.files.getFileList();
} catch (error: ErrorResponse) {
  if (error.code === 0) {
    // 网络连接失败
    console.error('网络连接失败，请检查网络设置');
  }
}
```

## 🔍 调试功能

### 启用调试模式

在 `.env.local` 中设置：

```env
VITE_ENABLE_DEBUG=true
```

启用后会在控制台显示：
- 🚀 API 请求信息
- ✅ API 响应信息  
- 🔧 应用配置信息

### 请求追踪

每个请求会自动添加 `X-Request-ID` 头，用于请求追踪和调试。

## 🎨 最佳实践

### 1. 类型安全

```typescript
import type { FileItem, FileListQuery } from '@/api';

const query: FileListQuery = {
  parentId: 'folder-id',
  type: 'file',
  page: 1,
  pageSize: 20
};

const response = await api.files.getFileList(query);
const files: FileItem[] = response.data.list;
```

### 2. 错误处理

```typescript
import type { ErrorResponse } from '@/api';

try {
  const result = await api.auth.login(credentials);
} catch (error: ErrorResponse) {
  // 结构化错误处理
  handleError(error);
}
```

### 3. 进度监控

```typescript
await api.files.uploadFile(file, parentId, (progress) => {
  // 更新上传进度条
  updateProgressBar(progress);
});
```

## 🔄 扩展指南

### 添加新的 API 服务

1. 在 `src/api/services/` 下创建新的服务文件
2. 定义相关的 TypeScript 接口
3. 实现 API 方法
4. 在 `src/api/index.ts` 中导出服务

```typescript
// src/api/services/notifications.ts
export const notificationsApi = {
  getNotifications: () => request.get('/notifications'),
  markAsRead: (id: string) => request.put(`/notifications/${id}/read`),
};

// src/api/index.ts
export { default as notificationsApi } from './services/notifications';

export const api = {
  auth: authApiService,
  files: filesApiService,
  notifications: notificationsApiService, // 新增
};
```

### 自定义拦截器

```typescript
import { http } from '@/api';

// 添加自定义请求拦截器
http.interceptors.request.use((config) => {
  // 自定义逻辑
  return config;
});

// 添加自定义响应拦截器
http.interceptors.response.use((response) => {
  // 自定义逻辑
  return response;
});
```

## 📚 相关文档

- [Axios 官方文档](https://axios-http.com/)
- [Vite 环境变量](https://vitejs.dev/guide/env-and-mode.html)
- [TypeScript 类型定义](https://www.typescriptlang.org/docs/)

---

**注意**: 在生产环境中，请确保正确配置 API 端点和安全设置。 