<template>
  <div>
    <div v-if="isLoading" class="flex flex-1 justify-center items-center">
      <div class="flex flex-col gap-4 items-center">
        <div class="w-8 h-8 rounded-full border-4 animate-spin border-primary border-r-transparent"></div>
        <p class="text-muted-foreground">正在加载权限管理系统...</p>
      </div>
    </div>
    <iframe v-show="!isLoading" :src="permissionsUrl" class="w-full h-full border-0" @load="handleIframeLoad"
      @error="handleIframeError" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'

const userStore = useUserStore()

const isLoading = ref(true)
const permissionsUrl = computed(() => userStore.permissionsUrl || '')

/**
 * 处理iframe加载完成
 */
const handleIframeLoad = () => {
  console.log('✅ 权限管理系统加载完成')
  isLoading.value = false
}

/**
 * 处理iframe加载错误
 */
const handleIframeError = () => {
  console.error('❌ 权限管理系统加载失败')
  isLoading.value = false
}
</script>