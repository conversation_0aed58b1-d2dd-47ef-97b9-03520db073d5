import { ref, computed, type ComputedRef } from "vue";
import { useStreamDownload, type DownloadConfig } from "./useStreamDownload";
import { useDownloadPath } from "./useDownloadPath";
import type { ItemType } from "@/types/files";
import { toast } from "vue-sonner";

export function useFileDownload(config: DownloadConfig | ComputedRef<DownloadConfig>) {
  const isDownloading = ref(false);

  // 确保配置是响应式的
  const downloadConfig = computed(() => {
    return typeof config === "object" && "value" in config ? config.value : config;
  });

  // 路径选择功能
  const downloadPath = useDownloadPath();

  // 下载功能
  const { downloadItem: streamDownloadItem, downloadMultipleItems: streamDownloadMultiple, downloadTasks, hasActiveDownloads } = useStreamDownload(downloadConfig);

  /**
   * 下载单个项目
   */
  const downloadItem = async (item: ItemType): Promise<void> => {
    if (isDownloading.value) {
      toast.warning("已有下载任务正在进行中，请稍后再试");
      return;
    }

    try {
      isDownloading.value = true;

      // 显示路径选择对话框
      const pathResult = await downloadPath.showPathSelection(item);

      if (pathResult.cancelled) {
        return;
      }

      if (!downloadPath.validatePath(pathResult.path)) {
        toast.error("选择的路径无效");
        return;
      }

      // 保存路径记忆
      if (pathResult.remember) {
        downloadPath.saveRememberedPath(pathResult.path);
      }

      toast.info(`开始下载: ${item.name}`);

      // 构建完整的文件路径（文件夹路径 + 文件名）
      // 确保路径分隔符正确处理
      const fullFilePath = pathResult.path.endsWith("/") ? `${pathResult.path}${item.name}` : `${pathResult.path}/${item.name}`;

      // 临时更新配置中的保存路径
      const originalSavePath = downloadConfig.value.savePath;
      downloadConfig.value.savePath = fullFilePath;

      try {
        await streamDownloadItem(item);
      } finally {
        // 恢复原始配置
        downloadConfig.value.savePath = originalSavePath;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "下载失败";
      toast.error(`下载失败: ${message}`);
      throw error;
    } finally {
      isDownloading.value = false;
    }
  };

  /**
   * 批量下载项目
   */
  const downloadMultipleItems = async (items: ItemType[]): Promise<void> => {
    if (isDownloading.value) {
      toast.warning("已有下载任务正在进行中，请稍后再试");
      return;
    }

    if (items.length === 0) {
      toast.warning("请选择要下载的项目");
      return;
    }

    try {
      isDownloading.value = true;

      // 显示路径选择对话框
      const pathResult = await downloadPath.showPathSelection(items);

      if (pathResult.cancelled) {
        return;
      }

      if (!downloadPath.validatePath(pathResult.path)) {
        toast.error("选择的路径无效");
        return;
      }

      // 保存路径记忆
      if (pathResult.remember) {
        downloadPath.saveRememberedPath(pathResult.path);
      }

      toast.info(`开始批量下载 ${items.length} 个项目`);

      // 对于批量下载，直接使用文件夹路径
      // 下载管理器会自动处理每个文件的完整路径
      const originalSavePath = downloadConfig.value.savePath;
      downloadConfig.value.savePath = pathResult.path;

      try {
        await streamDownloadMultiple(items);
      } finally {
        // 恢复原始配置
        downloadConfig.value.savePath = originalSavePath;
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "批量下载失败";
      toast.error(`批量下载失败: ${message}`);
      throw error;
    } finally {
      isDownloading.value = false;
    }
  };

  return {
    // 状态
    isDownloading,
    downloadTasks,
    hasActiveDownloads,

    // 路径选择状态
    isSelectingPath: downloadPath.isSelecting,
    showPathDialog: downloadPath.showPathDialog,
    downloadInfo: downloadPath.downloadInfo,

    // 方法
    downloadItem,
    downloadMultipleItems,

    // 路径选择方法
    handlePathConfirm: downloadPath.handlePathConfirm,
    handlePathCancel: downloadPath.handlePathCancel,
  };
}
