import { ref, computed, watchEffect } from "vue";
import { useRouter } from "vue-router";
import { watchAuthState, type AuthState, getAuthToken } from "./useAuth";
import { useSidebarStore } from "@/store/sidebar";
import { useUserStore } from "@/store/user";

/**
 * 应用守卫状态
 */
interface GuardState {
  isInitializing: boolean;
  shouldShowApp: boolean;
  shouldShowLogin: boolean;
  shouldShowLoading: boolean;
}

// 全局守卫状态
const guardState = ref<GuardState>({
  isInitializing: true,
  shouldShowApp: false,
  shouldShowLogin: false,
  shouldShowLoading: true,
});

/**
 * 应用认证守卫 composable
 */
export function useAuthGuard() {
  const router = useRouter();

  /**
   * 加载sidebar数据
   */
  const loadSidebarData = async () => {
    try {
      const sidebarStore = useSidebarStore();
      await sidebarStore.fetchSidebarData();
      console.log("✅ Sidebar数据加载成功");
    } catch (error) {
      console.warn("⚠️ Sidebar数据加载失败:", error);
      // 不抛出错误，避免影响应用正常运行
    }
  };

  /**
   * 根据认证状态更新守卫状态和路由
   */
  const updateGuardState = (authState: AuthState) => {
    const { isLoggedIn, isLoading } = authState;
    const currentRoute = router.currentRoute.value;
    const currentPath = currentRoute.path;

    console.log("🛡️ 更新守卫状态:", { isLoggedIn, isLoading, currentPath });

    if (isLoading) {
      // 认证加载中
      guardState.value = {
        isInitializing: false,
        shouldShowApp: false,
        shouldShowLogin: false,
        shouldShowLoading: true,
      };
      return;
    }

    if (isLoggedIn) {
      // 已登录状态
      guardState.value = {
        isInitializing: false,
        shouldShowApp: true,
        shouldShowLogin: false,
        shouldShowLoading: false,
      };

      // 登录成功后加载sidebar数据
      loadSidebarData();

      // 如果在登录页面，尝试恢复保存的路由或跳转到主页
      if (currentPath === "/login") {
        // 检查是否有保存的路由需要恢复
        try {
          const stored = sessionStorage.getItem("cloudDrive_lastRoute");
          if (stored) {
            const routeData = JSON.parse(stored);
            const age = Date.now() - routeData.timestamp;
            const maxAge = 24 * 60 * 60 * 1000; // 24小时

            if (age < maxAge && routeData.path !== "/login" && routeData.path !== "/resources") {
              console.log("✅ 已登录用户在登录页面，恢复保存的路由:", routeData);
              router.push({ path: routeData.path, query: routeData.query });
              return;
            }
          }
        } catch (error) {
          console.warn("恢复路由失败:", error);
        }

        console.log("✅ 已登录用户在登录页面，跳转到主页");
        router.push("/resources");
      }
    } else {
      // 未登录状态
      guardState.value = {
        isInitializing: false,
        shouldShowApp: false,
        shouldShowLogin: true,
        shouldShowLoading: false,
      };

      // 如果在受保护页面，跳转到登录页
      if (currentPath !== "/login") {
        console.log("❌ 未登录用户访问受保护页面，跳转到登录页");
        router.push("/login");
      }
    }
  };

  /**
   * 检查并恢复保存的路由
   */
  const checkAndRestoreRoute = () => {
    // 检查是否是页面刷新
    const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
    const isPageRefresh = navigation?.type === "reload";

    if (!isPageRefresh) {
      return false;
    }

    console.log("🔄 检测到页面刷新，检查是否需要恢复路由");

    try {
      const stored = sessionStorage.getItem("cloudDrive_lastRoute");
      if (stored) {
        const routeData = JSON.parse(stored);
        const age = Date.now() - routeData.timestamp;
        const maxAge = 24 * 60 * 60 * 1000; // 24小时

        if (age < maxAge && routeData.path !== "/login" && routeData.path !== "/resources") {
          const currentPath = router.currentRoute.value.path;

          // 如果当前路径是根路径或默认路径，且保存的路径不同，则恢复
          if ((currentPath === "/" || currentPath === "/resources") && routeData.path !== currentPath) {
            console.log("🔄 页面刷新后恢复保存的路由:", routeData);
            router.push({ path: routeData.path, query: routeData.query });
            return true;
          }
        }
      }
    } catch (error) {
      console.warn("恢复路由失败:", error);
    }

    return false;
  };

  /**
   * 验证token有效性
   */
  const validateTokenIfExists = async () => {
    const token = getAuthToken();
    if (token) {
      console.log("🔍 检测到token，验证有效性...");
      try {
        const userStore = useUserStore();
        await userStore.fetchUserInfo(false);
        console.log("✅ Token验证成功");
      } catch (error) {
        console.warn("❌ Token验证失败:", error);
        // 验证失败的处理已经在userStore中完成
      }
    }
  };

  /**
   * 初始化守卫
   */
  const initGuard = () => {
    console.log("🛡️ 初始化认证守卫...");

    // 监听认证状态变化
    const authStateRef = watchAuthState();

    watchEffect(() => {
      updateGuardState(authStateRef.value);
    });

    // 如果有token但用户状态未登录，验证token有效性
    setTimeout(() => {
      if (!authStateRef.value.isLoggedIn && getAuthToken()) {
        validateTokenIfExists();
      }
    }, 50);

    // 标记初始化完成
    setTimeout(() => {
      if (guardState.value.isInitializing) {
        guardState.value.isInitializing = false;
        console.log("🛡️ 守卫初始化完成");

        // 初始化完成后，如果用户已登录，检查是否需要恢复路由
        if (authStateRef.value.isLoggedIn) {
          setTimeout(() => {
            checkAndRestoreRoute();
          }, 200); // 再延迟一点，确保路由系统完全就绪
        }
      }
    }, 100);
  };

  return {
    // 状态
    isInitializing: computed(() => guardState.value.isInitializing),
    shouldShowApp: computed(() => guardState.value.shouldShowApp),
    shouldShowLogin: computed(() => guardState.value.shouldShowLogin),
    shouldShowLoading: computed(() => guardState.value.shouldShowLoading),

    // 方法
    initGuard,
  };
}

/**
 * 全局认证守卫（简化版，用于App.vue）
 */
export function useGlobalAuthGuard() {
  return {
    // 状态
    shouldShowApp: computed(() => guardState.value.shouldShowApp),
    shouldShowLogin: computed(() => guardState.value.shouldShowLogin),
    shouldShowLoading: computed(() => guardState.value.isInitializing || guardState.value.shouldShowLoading),

    // 方法
    initAuthGuard: () => {
      const guard = useAuthGuard();
      guard.initGuard();
    },
  };
}

/**
 * 保存路由到 sessionStorage
 */
const saveRouteToStorage = (path: string, query: Record<string, any> = {}) => {
  try {
    const routeData = {
      path,
      query,
      timestamp: Date.now(),
    };
    sessionStorage.setItem("cloudDrive_lastRoute", JSON.stringify(routeData));
    console.log("💾 路由守卫保存当前路由:", routeData);
  } catch (error) {
    console.warn("保存路由失败:", error);
  }
};

/**
 * 路由守卫工厂函数
 */
export function createRouteGuard() {
  return (to: any, from: any, next: any) => {
    const authStateRef = watchAuthState();
    const { isLoggedIn } = authStateRef.value;

    const requiresAuth = to.matched.some((record: any) => record.meta.requiresAuth);

    console.log("🛡️ 路由守卫检查:", {
      to: to.path,
      from: from?.path,
      requiresAuth,
      isLoggedIn,
    });

    // 保存当前路由（如果是已登录用户访问深层路径）
    if (requiresAuth && isLoggedIn && to.path !== "/login" && to.path !== "/" && to.path !== "/resources") {
      // 延迟保存，确保路由完全加载
      setTimeout(() => saveRouteToStorage(to.path, to.query || {}), 100);
    }

    if (requiresAuth && !isLoggedIn) {
      console.log("❌ 未认证访问受保护页面，跳转到登录页");
      next("/login");
    } else if (to.path === "/login" && isLoggedIn) {
      console.log("✅ 已认证用户访问登录页，跳转到主页");
      next("/resources");
    } else {
      next();
    }
  };
}
