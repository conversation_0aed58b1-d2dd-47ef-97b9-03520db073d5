<template>
  <div class="flex relative justify-center items-center w-full h-[90vh] bg-black/5">
    <!-- 加载状态 -->
    <div v-if="isLoading"
      class="flex absolute inset-0 z-10 justify-center items-center backdrop-blur-sm bg-background/80">
      <div class="flex flex-col gap-2 items-center">
        <div class="w-8 h-8 rounded-full border-b-2 animate-spin border-primary"></div>
        <p class="text-sm text-muted-foreground">加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex flex-col gap-4 items-center p-8 text-center">
      <div class="flex justify-center items-center w-16 h-16 rounded-full bg-destructive/10">
        <svg class="w-8 h-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div>
        <h3 class="mb-1 font-medium text-foreground">图片加载失败</h3>
        <p class="text-sm text-muted-foreground">{{ error }}</p>
      </div>
      <button @click="retryLoad"
        class="px-4 py-2 rounded-md transition-colors bg-primary text-primary-foreground hover:bg-primary/90">
        重试
      </button>
    </div>

    <!-- 图片预览 -->
    <div v-else class="flex overflow-hidden relative justify-center items-center w-full h-full" @wheel="handleWheel"
      @mousedown="handleMouseDown" @mousemove="handleMouseMove" @mouseup="handleMouseUp" @mouseleave="handleMouseUp">
      <img ref="imageRef" :src="imageUrl" :alt="fileName || '预览图片'"
        class="max-w-full max-h-full transition-transform duration-200 ease-out cursor-grab active:cursor-grabbing"
        :style="imageStyle" @load="handleImageLoad" @error="handleImageError" @dragstart.prevent />

      <!-- 缩放控制 -->
      <div
        class="flex absolute bottom-4 left-1/2 gap-2 items-center px-3 py-2 rounded-lg shadow-lg backdrop-blur-sm transform -translate-x-1/2 bg-background/90">
        <button @click="zoomOut" :disabled="scale <= minScale"
          class="p-1 rounded outline-none hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />
          </svg>
        </button>

        <span class="text-sm font-medium min-w-[3rem] text-center">
          {{ Math.round(scale * 100) }}%
        </span>

        <button @click="zoomIn" :disabled="scale >= maxScale"
          class="p-1 rounded outline-none hover:bg-accent disabled:opacity-50 disabled:cursor-not-allowed">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        </button>

        <div class="mx-1 w-px h-4 bg-border"></div>

        <button @click="resetZoom" class="p-1 text-xs rounded hover:bg-accent">
          重置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'

interface Props {
  imageUrl: string
  fileName?: string
  isLoading?: boolean
  error?: string | null
}

interface Emits {
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  error: null
})

const emit = defineEmits<Emits>()

// 图片元素引用
const imageRef = ref<HTMLImageElement>()

// 缩放和平移状态
const scale = ref(1)
const translateX = ref(0)
const translateY = ref(0)

// 缩放限制
const minScale = 0.1
const maxScale = 5

// 拖拽状态
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const dragStartTranslate = ref({ x: 0, y: 0 })

// 计算图片样式
const imageStyle = computed(() => ({
  transform: `scale(${scale.value}) translate(${translateX.value}px, ${translateY.value}px)`,
  transformOrigin: 'center center'
}))

// 监听URL变化，重置状态
watch(() => props.imageUrl, () => {
  resetZoom()
}, { immediate: true })

/**
 * 处理图片加载成功
 */
function handleImageLoad() {
  // 图片加载成功后，自动适应容器大小
  nextTick(() => {
    fitToContainer()
  })
}

/**
 * 处理图片加载错误
 */
function handleImageError() {
  // 错误处理由父组件通过props传入
}

/**
 * 重试加载
 */
function retryLoad() {
  emit('retry')
}

/**
 * 适应容器大小
 */
function fitToContainer() {
  if (!imageRef.value) return

  const container = imageRef.value.parentElement
  if (!container) return

  // 获取容器和图片的实际尺寸
  const containerRect = container.getBoundingClientRect()
  const imageNaturalWidth = imageRef.value.naturalWidth
  const imageNaturalHeight = imageRef.value.naturalHeight

  if (imageNaturalWidth === 0 || imageNaturalHeight === 0) return

  // 计算适合容器的缩放比例，留出一些边距
  const padding = 40 // 40px 边距
  const availableWidth = containerRect.width - padding
  const availableHeight = containerRect.height - padding

  const scaleX = availableWidth / imageNaturalWidth
  const scaleY = availableHeight / imageNaturalHeight
  const newScale = Math.min(scaleX, scaleY, 1) // 不超过原始大小

  // 重置缩放和位置，确保居中
  scale.value = newScale
  translateX.value = 0
  translateY.value = 0
}

/**
 * 放大
 */
function zoomIn() {
  const newScale = Math.min(scale.value * 1.2, maxScale)
  scale.value = newScale
}

/**
 * 缩小
 */
function zoomOut() {
  const newScale = Math.max(scale.value / 1.2, minScale)
  scale.value = newScale
}

/**
 * 重置缩放
 */
function resetZoom() {
  // 直接调用 fitToContainer 来重置到最佳显示状态
  nextTick(() => {
    fitToContainer()
  })
}

/**
 * 处理鼠标滚轮缩放
 */
function handleWheel(event: WheelEvent) {
  event.preventDefault()

  const delta = event.deltaY > 0 ? 0.9 : 1.1
  const newScale = Math.max(minScale, Math.min(maxScale, scale.value * delta))

  scale.value = newScale
}

/**
 * 处理鼠标按下
 */
function handleMouseDown(event: MouseEvent) {
  if (event.button !== 0) return // 只处理左键

  isDragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  dragStartTranslate.value = { x: translateX.value, y: translateY.value }

  event.preventDefault()
}

/**
 * 处理鼠标移动
 */
function handleMouseMove(event: MouseEvent) {
  if (!isDragging.value) return

  const deltaX = event.clientX - dragStart.value.x
  const deltaY = event.clientY - dragStart.value.y

  translateX.value = dragStartTranslate.value.x + deltaX / scale.value
  translateY.value = dragStartTranslate.value.y + deltaY / scale.value
}

/**
 * 处理鼠标抬起
 */
function handleMouseUp() {
  isDragging.value = false
}

// 组件挂载时初始化
onMounted(() => {
  // 如果图片已经加载完成，立即适应容器
  if (imageRef.value && imageRef.value.complete && imageRef.value.naturalWidth > 0) {
    nextTick(() => {
      fitToContainer()
    })
  }
})
</script>
