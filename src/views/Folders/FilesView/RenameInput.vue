<template>
  <div class="flex items-center gap-1" data-rename-container @click.stop>
    <Input :ref="inputRef" :model-value="modelValue" :class="inputClass"
      @update:model-value="$emit('update:modelValue', String($event))" @keydown="handleKeydown" @click.stop />
    <button @click.stop="$emit('confirm')"
      class="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded bg-green-500 hover:bg-green-600 text-white transition-colors">
      <Check class="w-3 h-3" />
    </button>
    <button @click.stop="$emit('cancel')"
      class="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded bg-red-500 hover:bg-red-600 text-white transition-colors">
      <X class="w-3 h-3" />
    </button>
  </div>
</template>

<script setup lang="ts">
import { Check, X } from 'lucide-vue-next'
import { Input } from '@/components/ui/input'

// Props
defineProps<{
  modelValue: string
  inputClass?: string
  inputRef?: any
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: string]
  confirm: []
  cancel: []
}>()

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    emit('confirm')
  }
}
</script>