/**
 * 调试工具类
 * 统一管理项目中的调试信息，只在开发环境输出
 */

type LogLevel = "info" | "warn" | "error" | "debug";

interface LogContext {
  module?: string;
  action?: string;
  data?: any;
}

class DebugManager {
  private isDev = import.meta.env.DEV;
  private enabledModules = new Set<string>();

  constructor() {
    // 默认启用重要模块的调试
    this.enabledModules.add("upload");
    this.enabledModules.add("error");
    this.enabledModules.add("auth");
  }

  /**
   * 启用特定模块的调试
   */
  enable(module: string) {
    this.enabledModules.add(module);
  }

  /**
   * 禁用特定模块的调试
   */
  disable(module: string) {
    this.enabledModules.delete(module);
  }

  /**
   * 检查模块是否启用调试
   */
  private isEnabled(module?: string): boolean {
    if (!this.isDev) return false;
    if (!module) return true;
    return this.enabledModules.has(module);
  }

  /**
   * 格式化日志前缀
   */
  private formatPrefix(level: LogLevel, context?: LogContext): string {
    const timestamp = new Date().toLocaleTimeString();
    const levelIcon = {
      info: "📝",
      warn: "⚠️",
      error: "❌",
      debug: "🔧",
    }[level];

    let prefix = `[${timestamp}] ${levelIcon}`;

    if (context?.module) {
      prefix += ` [${context.module.toUpperCase()}]`;
    }

    if (context?.action) {
      prefix += ` ${context.action}:`;
    }

    return prefix;
  }

  /**
   * 通用日志方法
   */
  private log(level: LogLevel, message: string, context?: LogContext) {
    if (!this.isEnabled(context?.module)) return;

    const prefix = this.formatPrefix(level, context);
    const method = level === "warn" ? console.warn : level === "error" ? console.error : console.log;

    if (context?.data) {
      method(prefix, message, context.data);
    } else {
      method(prefix, message);
    }
  }

  /**
   * 信息日志 - 用于一般操作反馈
   */
  info(message: string, context?: LogContext) {
    this.log("info", message, context);
  }

  /**
   * 警告日志 - 用于潜在问题
   */
  warn(message: string, context?: LogContext) {
    this.log("warn", message, context);
  }

  /**
   * 错误日志 - 用于错误和异常
   */
  error(message: string, context?: LogContext) {
    this.log("error", message, context);
  }

  /**
   * 调试日志 - 用于详细的调试信息
   */
  debug(message: string, context?: LogContext) {
    this.log("debug", message, context);
  }

  /**
   * 上传相关日志
   */
  upload = {
    start: (fileName: string, size?: number) => this.info(`开始上传: ${fileName}${size ? ` (${this.formatSize(size)})` : ""}`, { module: "upload", action: "start" }),

    progress: (fileName: string, progress: number) => this.debug(`上传进度: ${fileName} - ${progress}%`, { module: "upload", action: "progress" }),

    complete: (fileName: string) => this.info(`上传完成: ${fileName}`, { module: "upload", action: "complete" }),

    error: (fileName: string, error: string) => this.error(`上传失败: ${fileName} - ${error}`, { module: "upload", action: "error" }),

    batch: (batchName: string, fileCount: number) => this.info(`批量上传: ${batchName} (${fileCount}个文件)`, { module: "upload", action: "batch" }),
  };

  /**
   * 任务管理相关日志
   */
  task = {
    create: (taskId: string, fileName: string) => this.debug(`任务创建: ${fileName} (${taskId.substring(0, 8)}...)`, { module: "task", action: "create" }),

    status: (fileName: string, from: string, to: string) => this.debug(`任务状态变化: ${fileName} - ${from} -> ${to}`, { module: "task", action: "status" }),

    remove: (fileName: string, status: string) => this.info(`任务移除: ${fileName} (${status})`, { module: "task", action: "remove" }),
  };

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    const units = ["B", "KB", "MB", "GB"];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }
}

// 导出单例实例
export const debug = new DebugManager();

// 兼容性方法 - 逐步替换现有的 console.log
export const logger = {
  info: (message: string, data?: any) => debug.info(message, { data }),
  warn: (message: string, data?: any) => debug.warn(message, { data }),
  error: (message: string, data?: any) => debug.error(message, { data }),
  debug: (message: string, data?: any) => debug.debug(message, { data }),
};
