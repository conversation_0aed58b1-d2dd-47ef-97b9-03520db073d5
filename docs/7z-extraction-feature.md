# 7z文件自动解压缩功能

## 功能概述

为Electron应用的下载管理器添加了完整的7z文件自动解压缩功能，当检测到下载文件扩展名为.7z时，在下载完成后自动触发解压缩流程。

## 核心特性

### 🚀 自动触发
- 自动检测7z文件扩展名
- 下载完成后立即触发解压缩流程
- 无需用户手动操作

### ⚡ 高性能解压缩
- 使用Node.js的7zip-bin库实现高性能解压缩
- Worker线程解压缩，避免阻塞主线程
- 支持大文件解压缩，使用流式处理避免内存溢出

### 📊 实时进度显示
- 独立的解压缩进度指示器，区别于下载进度
- 实时进度条和剩余时间估算
- 显示当前解压文件和已解压文件数量

### 🎛️ 完整控制
- 暂停/恢复/取消解压缩操作
- 解压缩任务队列管理，支持并发控制
- 批量解压缩操作支持

### 🔐 安全处理
- 密码保护的7z文件支持（提示用户输入密码）
- 文件冲突处理（提供重命名/覆盖/跳过选项）
- 完善的错误处理和重试机制

### 🔔 用户体验
- 系统通知集成
- 解压完成后询问用户是否删除原始7z文件
- 下载历史记录中显示解压缩状态

## 技术架构

### 模块结构
```
electron/7z-extractor/
├── types.ts                    # 类型定义
├── extractionManager.ts        # 解压缩管理器
├── extractionWorker.ts         # Worker线程管理器
├── worker.ts                   # Worker线程脚本
├── taskQueue.ts                # 任务队列管理
├── errorHandler.ts             # 错误处理器
├── notificationManager.ts      # 通知管理器
├── ipcHandlers.ts              # IPC处理器
├── preloadApi.ts               # Preload API
└── index.ts                    # 模块入口
```

### 前端组件
```
src/components/download/
├── ExtractionProgressCard.vue  # 解压缩进度卡片
└── ExtractionTaskList.vue      # 解压缩任务列表
```

### 集成点
- `electron/main.ts` - 主进程初始化
- `electron/preload.ts` - API暴露
- `src/composables/useStreamDownloadManager.ts` - 下载管理器集成
- `src/types/electron.d.ts` - TypeScript类型定义

## 使用流程

### 自动解压缩流程
1. 用户下载7z文件
2. 下载完成后自动检测文件扩展名
3. 创建解压缩任务并开始解压缩
4. 显示解压缩进度和状态
5. 解压缩完成后通知用户
6. 可选择删除原始7z文件

### 手动控制
- 暂停：暂停当前解压缩操作
- 恢复：恢复暂停的解压缩操作
- 取消：取消解压缩并清理部分文件
- 重试：重新开始失败的解压缩任务

### 密码处理
1. 检测到密码保护的7z文件
2. 显示密码输入对话框
3. 用户输入密码后重新开始解压缩
4. 密码错误时重新提示

### 文件冲突处理
1. 检测到文件名冲突
2. 提供三种选择：覆盖、跳过、重命名
3. 可选择应用到所有冲突文件
4. 根据用户选择继续解压缩

## 配置选项

### 解压缩配置
```typescript
interface ExtractionConfig {
  maxConcurrent?: number;              // 最大并发解压任务数，默认 2
  timeout?: number;                    // 超时时间，默认 5分钟
  deleteOriginalAfterExtraction?: boolean; // 解压完成后是否删除原文件，默认 false
  overwriteExisting?: boolean;         // 是否覆盖已存在的文件，默认 false
  createSubfolder?: boolean;           // 是否创建同名子文件夹，默认 true
  passwordPromptTimeout?: number;      // 密码输入超时时间，默认 30秒
}
```

### 通知配置
```typescript
interface NotificationConfig {
  enabled?: boolean;                   // 是否启用通知，默认 true
  showProgress?: boolean;              // 是否显示进度通知，默认 false
  showCompletion?: boolean;            // 是否显示完成通知，默认 true
  showErrors?: boolean;                // 是否显示错误通知，默认 true
  playSound?: boolean;                 // 是否播放声音，默认 true
  autoHide?: boolean;                  // 是否自动隐藏，默认 true
  autoHideDelay?: number;              // 自动隐藏延迟，默认 5秒
}
```

## API接口

### 主要方法
- `createExtractionTask()` - 创建解压缩任务
- `startExtraction()` - 开始解压缩
- `pauseExtraction()` - 暂停解压缩
- `resumeExtraction()` - 恢复解压缩
- `cancelExtraction()` - 取消解压缩
- `deleteTask()` - 删除任务

### 事件监听
- `task-created` - 任务创建
- `task-progress` - 进度更新
- `task-status-changed` - 状态变化
- `task-completed` - 任务完成
- `task-error` - 任务错误
- `password-required` - 需要密码
- `file-conflict` - 文件冲突

## 性能优化

### Worker线程
- 使用Worker线程进行解压缩操作
- 避免阻塞主线程UI
- 支持多个Worker并发处理

### 任务队列
- 智能任务调度
- 并发控制
- 优先级管理

### 内存管理
- 流式解压缩处理
- 避免大文件内存溢出
- 及时清理临时资源

## 错误处理

### 常见错误类型
- 密码错误
- 文件损坏
- 磁盘空间不足
- 权限不足
- 文件冲突

### 处理策略
- 自动重试机制
- 用户友好的错误提示
- 详细的错误日志
- 优雅的降级处理

## 兼容性

### 支持的平台
- Windows (x64)
- macOS (x64, arm64)
- Linux (x64)

### 支持的7z格式
- 标准7z压缩包
- 密码保护的7z文件
- 多卷7z文件（部分支持）

## 未来扩展

### 计划功能
- 支持更多压缩格式（zip, rar等）
- 压缩功能
- 解压缩预览
- 批量压缩/解压缩
- 云端解压缩

### 性能优化
- 增量解压缩
- 智能缓存
- 网络解压缩
- GPU加速（如果可用）

## 注意事项

1. **依赖要求**：需要安装7zip-bin和node-7z包
2. **权限要求**：需要文件系统读写权限
3. **内存使用**：大文件解压缩时注意内存使用
4. **并发限制**：建议不超过4个并发解压缩任务
5. **错误处理**：确保正确处理各种异常情况

## 总结

7z文件自动解压缩功能为Electron应用提供了完整的压缩文件处理能力，通过高性能的Worker线程实现、完善的用户交互和错误处理，为用户提供了流畅的解压缩体验。该功能与现有的下载管理器、任务管理和通知系统完全集成，形成了一个完整的文件处理生态系统。
