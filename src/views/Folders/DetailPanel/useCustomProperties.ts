import { computed, ref, type ComputedRef } from "vue";
import type { ItemType } from "@/types/files";
import filesApi from "@/api/services/files";
import { toast } from "vue-sonner";

// API 筛选项类型定义
export interface ApiFilterOption {
  value: number | string;
  name: string;
}

export interface ApiFilterGroup {
  field_name: string;
  label: string;
  type: "select" | "multiselect"; // 用于筛选功能
  upload_type: "select" | "multiselect"; // 用于上传和编辑功能
  source: string;
  options: ApiFilterOption[];
  is_form: string; // 是否在表单中显示 "Y" 或 "N"
  is_search: string; // 是否在搜索中显示 "Y" 或 "N"
}

// 编辑状态接口
export interface EditableProperty {
  field_name: string;
  label: string;
  upload_type: "select" | "multiselect"; // 使用 upload_type 用于编辑功能
  options: ApiFilterOption[];
  currentValue: any;
  originalValue: any;
}

/**
 * 自定义属性处理 composable
 */
export function useCustomProperties(selectedItem: ComputedRef<ItemType | null>, apiFilterOptions: ComputedRef<ApiFilterGroup[]>) {
  // 编辑状态管理
  const isEditing = ref(false);
  const editingProperties = ref<Record<string, any>>({});
  const isSaving = ref(false);
  /**
   * 计算自定义属性显示
   */
  const customProperties = computed(() => {
    if (!selectedItem.value || !selectedItem.value.category_fields) {
      return null;
    }

    const categoryFields = selectedItem.value.category_fields;
    const customProps: Record<string, any> = {};

    // 遍历过滤选项，找到匹配的字段
    apiFilterOptions.value.forEach((filterGroup) => {
      const fieldName = filterGroup.field_name;
      const fieldValue = categoryFields[fieldName];

      // 只显示存在值的字段（包括数组长度为0的情况也要检查）
      if (fieldValue !== undefined && fieldValue !== null && fieldValue !== "") {
        // 检查是否有有效的选项列表
        const hasValidOptions = filterGroup.options && Array.isArray(filterGroup.options) && filterGroup.options.length > 0;

        // 根据字段类型处理值的显示（使用 upload_type 用于编辑功能的显示）
        if (filterGroup.upload_type === "multiselect" && Array.isArray(fieldValue) && hasValidOptions) {
          // 多选字段：将ID数组转换为名称数组
          const displayValues = fieldValue
            .filter((id) => id !== null && id !== undefined && id !== "") // 过滤无效值
            .map((id) => {
              // 确保类型匹配，处理数字和字符串ID
              const option = filterGroup.options.find((opt) => String(opt.value) === String(id) || opt.value === id);
              return option ? option.name : String(id);
            });
          if (displayValues.length > 0) {
            customProps[filterGroup.label] = displayValues;
          }
        } else if (filterGroup.upload_type === "select" && hasValidOptions) {
          // 单选字段：将ID转换为名称
          const option = filterGroup.options.find((opt) => String(opt.value) === String(fieldValue) || opt.value === fieldValue);
          customProps[filterGroup.label] = option ? option.name : String(fieldValue);
        } else {
          // 其他类型字段或没有选项的字段直接显示原始值
          customProps[filterGroup.label] = fieldValue;
        }
      }
    });

    // 如果没有自定义属性，返回 null
    return Object.keys(customProps).length > 0 ? customProps : null;
  });

  /**
   * 格式化属性值用于显示
   */
  const formatPropertyValue = (value: any): string => {
    if (value === null || value === undefined) {
      return "无";
    }

    if (typeof value === "boolean") {
      return value ? "是" : "否";
    }

    if (typeof value === "object") {
      if (Array.isArray(value)) {
        return value.join(", ");
      }
      return JSON.stringify(value, null, 2);
    }

    if (typeof value === "number") {
      return value.toLocaleString("zh-CN");
    }

    // 处理特殊的字符串值
    if (typeof value === "string") {
      const trimmedValue = value.trim();
      // 处理 Y/N 值
      if (trimmedValue === "Y") return "是";
      if (trimmedValue === "N") return "否";
      // 处理其他布尔值字符串
      if (trimmedValue === "true" || trimmedValue === "True" || trimmedValue === "TRUE") return "是";
      if (trimmedValue === "false" || trimmedValue === "False" || trimmedValue === "FALSE") return "否";
      // 处理数字字符串的布尔值
      if (trimmedValue === "1") return "是";
      if (trimmedValue === "0") return "否";
    }

    return String(value);
  };

  /**
   * 获取值的样式类
   */
  const getValueClass = (value: any): string => {
    // 统一处理所有表示"是"的值
    const positiveValues = ["Y", "是", "true", "True", "TRUE", "有", "1", 1, true];
    // 统一处理所有表示"否"的值
    const negativeValues = ["N", "否", "false", "False", "FALSE", "无", "0", 0, false];

    if (typeof value === "boolean") {
      return value ? "text-green-600" : "text-red-600";
    }

    if (typeof value === "string") {
      const trimmedValue = value.trim();
      if (positiveValues.includes(trimmedValue)) {
        return "text-green-600";
      }
      if (negativeValues.includes(trimmedValue)) {
        return "text-red-600";
      }
    }

    if (typeof value === "number") {
      if (positiveValues.includes(value)) {
        return "text-green-600";
      }
      if (negativeValues.includes(value)) {
        return "text-red-600";
      }
      return "font-mono";
    }

    if (typeof value === "object") {
      return "font-mono text-xs";
    }

    return "";
  };

  /**
   * 获取可编辑的属性列表
   */
  const editableProperties = computed((): EditableProperty[] => {
    if (!selectedItem.value || !selectedItem.value.category_fields) {
      return [];
    }

    const categoryFields = selectedItem.value.category_fields;
    const properties: EditableProperty[] = [];

    // 遍历过滤选项，找到匹配的字段
    apiFilterOptions.value.forEach((filterGroup) => {
      const fieldName = filterGroup.field_name;
      const fieldValue = categoryFields[fieldName];

      // 只包含 select 和 multiselect 类型的字段（使用 upload_type）
      if (filterGroup.upload_type === "select" || filterGroup.upload_type === "multiselect") {
        properties.push({
          field_name: fieldName,
          label: filterGroup.label,
          upload_type: filterGroup.upload_type,
          options: filterGroup.options,
          currentValue: fieldValue,
          originalValue: fieldValue,
        });
      }
    });

    return properties;
  });

  /**
   * 开始编辑
   */
  const startEditing = () => {
    if (!selectedItem.value || !selectedItem.value.category_fields) {
      return;
    }

    // 初始化编辑数据
    editingProperties.value = { ...selectedItem.value.category_fields };
    isEditing.value = true;
  };

  /**
   * 取消编辑
   */
  const cancelEditing = () => {
    editingProperties.value = {};
    isEditing.value = false;
  };

  /**
   * 保存编辑
   */
  const saveEditing = async () => {
    if (!selectedItem.value || isSaving.value) {
      return;
    }

    isSaving.value = true;

    try {
      // 获取 category_id，优先从 selectedItem，然后从 URL 参数
      let categoryId = selectedItem.value.category_id;

      // 如果 selectedItem 中没有 category_id，尝试从 URL 参数获取
      if (!categoryId) {
        const urlParams = new URLSearchParams(window.location.search);
        const urlCategoryId = urlParams.get("categoryId");
        categoryId = urlCategoryId ? Number(urlCategoryId) : undefined;
      }

      // 验证必要字段
      if (!categoryId) {
        throw new Error("缺少必要的 category_id 字段，请检查数据结构或 URL 参数");
      }

      // 构建更新数据
      const updateData = {
        category_id: categoryId,
        file_id: selectedItem.value.id,
        ...editingProperties.value,
      };

      // 调用 API 更新文件信息
      const response = await filesApi.editFileInfo(updateData);

      if (response.code === 0) {
        // 更新成功，使用后端返回的消息或默认消息
        toast.success(response.msg || "自定义属性更新成功");

        // 更新本地数据
        if (selectedItem.value.category_fields) {
          Object.assign(selectedItem.value.category_fields, editingProperties.value);
        }

        // 退出编辑模式
        isEditing.value = false;
        editingProperties.value = {};
      } else {
        // 更新失败，显示错误信息
        toast.error(response.msg || "自定义属性更新失败");
      }
    } catch (error) {
      console.error("保存自定义属性时出错:", error);
      const errorMessage = error instanceof Error ? error.message : "保存失败";
      toast.error(`保存失败: ${errorMessage}`);
    } finally {
      isSaving.value = false;
    }
  };

  /**
   * 更新编辑中的属性值
   */
  const updateEditingProperty = (fieldName: string, value: any) => {
    editingProperties.value[fieldName] = value;
  };

  /**
   * 调试函数：验证 category_id 是否正确获取
   */
  const debugCategoryId = () => {
    if (!selectedItem.value) {
      console.log("🔍 debugCategoryId: 没有选中项");
      return;
    }

    console.log("🔍 debugCategoryId: 详细信息", {
      selectedItem: selectedItem.value,
      category_id: selectedItem.value.category_id,
      hasCategory_id: "category_id" in selectedItem.value,
      allKeys: Object.keys(selectedItem.value),
      urlCategoryId: new URLSearchParams(window.location.search).get("categoryId"),
    });
  };

  return {
    customProperties,
    formatPropertyValue,
    getValueClass,
    // 编辑相关
    isEditing,
    editingProperties,
    isSaving,
    editableProperties,
    startEditing,
    cancelEditing,
    saveEditing,
    updateEditingProperty,
    // 调试相关
    debugCategoryId,
  };
}
