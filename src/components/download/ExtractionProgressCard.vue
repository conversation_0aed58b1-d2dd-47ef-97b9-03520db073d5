<template>
  <div class="extraction-progress-card bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4 mb-3">
    <!-- 文件信息头部 -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-amber-100 dark:bg-amber-900 rounded-lg flex items-center justify-center">
            <svg class="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
        </div>
        <div class="flex-1 min-w-0">
          <h3 class="text-sm font-medium text-gray-900 dark:text-white truncate">
            {{ task.fileName }}
          </h3>
          <p class="text-xs text-gray-500 dark:text-gray-400">
            解压缩到: {{ task.extractPath }}
          </p>
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="flex items-center space-x-2">
        <span :class="statusClasses" class="px-2 py-1 text-xs font-medium rounded-full">
          {{ statusText }}
        </span>
        <button
          v-if="canDelete"
          @click="$emit('delete', task.id)"
          class="text-gray-400 hover:text-red-500 transition-colors"
          title="删除任务"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    </div>

    <!-- 进度条 -->
    <div class="mb-3">
      <div class="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
        <span>解压缩进度</span>
        <span>{{ task.extractionProgress || 0 }}%</span>
      </div>
      <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
        <div
          :class="progressBarClasses"
          class="h-2 rounded-full transition-all duration-300"
          :style="{ width: `${task.extractionProgress || 0}%` }"
        ></div>
      </div>
    </div>

    <!-- 详细信息 -->
    <div class="grid grid-cols-2 gap-4 text-xs text-gray-500 dark:text-gray-400 mb-3">
      <div>
        <span class="font-medium">已解压文件:</span>
        <span class="ml-1">{{ task.extractedFiles || 0 }}{{ task.totalFiles ? `/${task.totalFiles}` : '' }}</span>
      </div>
      <div>
        <span class="font-medium">解压速度:</span>
        <span class="ml-1">{{ formatSpeed(task.speed || 0) }}</span>
      </div>
      <div>
        <span class="font-medium">剩余时间:</span>
        <span class="ml-1">{{ formatTime(task.remainingTime || 0) }}</span>
      </div>
      <div v-if="task.currentFile">
        <span class="font-medium">当前文件:</span>
        <span class="ml-1 truncate">{{ task.currentFile }}</span>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="task.extractionError" class="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-600 dark:text-red-400">
      {{ task.extractionError }}
    </div>

    <!-- 操作按钮 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <!-- 暂停/恢复按钮 -->
        <button
          v-if="canPause"
          @click="$emit('pause', task.id)"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-amber-700 bg-amber-100 hover:bg-amber-200 dark:bg-amber-900 dark:text-amber-300 dark:hover:bg-amber-800 rounded-md transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          暂停
        </button>
        
        <button
          v-if="canResume"
          @click="$emit('resume', task.id)"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 rounded-md transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m6-6a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          恢复
        </button>

        <!-- 取消按钮 -->
        <button
          v-if="canCancel"
          @click="$emit('cancel', task.id)"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800 rounded-md transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
          取消
        </button>

        <!-- 重试按钮 -->
        <button
          v-if="canRetry"
          @click="$emit('retry', task.id)"
          class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 dark:bg-green-900 dark:text-green-300 dark:hover:bg-green-800 rounded-md transition-colors"
        >
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          重试
        </button>
      </div>

      <!-- 完成时间 -->
      <div v-if="task.endTime" class="text-xs text-gray-500 dark:text-gray-400">
        {{ formatDateTime(task.endTime) }}
      </div>
    </div>

    <!-- 密码输入对话框 -->
    <div v-if="showPasswordDialog" class="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
      <div class="flex items-center mb-2">
        <svg class="w-4 h-4 text-yellow-600 dark:text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        <span class="text-sm font-medium text-yellow-800 dark:text-yellow-200">需要密码</span>
      </div>
      <div class="flex items-center space-x-2">
        <input
          v-model="password"
          type="password"
          placeholder="请输入解压密码"
          class="flex-1 px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
          @keyup.enter="submitPassword"
        />
        <button
          @click="submitPassword"
          class="px-3 py-1 text-xs font-medium text-white bg-blue-600 hover:bg-blue-700 rounded transition-colors"
        >
          确认
        </button>
        <button
          @click="cancelPassword"
          class="px-3 py-1 text-xs font-medium text-gray-600 bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 rounded transition-colors"
        >
          取消
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { DownloadTask } from '@/composables/useStreamDownloadManager';

interface Props {
  task: DownloadTask;
  showPasswordDialog?: boolean;
}

interface Emits {
  (e: 'pause', taskId: string): void;
  (e: 'resume', taskId: string): void;
  (e: 'cancel', taskId: string): void;
  (e: 'retry', taskId: string): void;
  (e: 'delete', taskId: string): void;
  (e: 'password', taskId: string, password: string): void;
  (e: 'password-cancel', taskId: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  showPasswordDialog: false,
});

const emit = defineEmits<Emits>();

const password = ref('');

// 计算属性
const statusText = computed(() => {
  switch (props.task.extractionStatus) {
    case 'pending': return '等待中';
    case 'extracting': return '解压中';
    case 'paused': return '已暂停';
    case 'completed': return '已完成';
    case 'error': return '解压失败';
    case 'cancelled': return '已取消';
    default: return '未知';
  }
});

const statusClasses = computed(() => {
  switch (props.task.extractionStatus) {
    case 'pending': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    case 'extracting': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'paused': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'error': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  }
});

const progressBarClasses = computed(() => {
  switch (props.task.extractionStatus) {
    case 'extracting': return 'bg-blue-500';
    case 'paused': return 'bg-yellow-500';
    case 'completed': return 'bg-green-500';
    case 'error': return 'bg-red-500';
    default: return 'bg-gray-500';
  }
});

const canPause = computed(() => props.task.extractionStatus === 'extracting');
const canResume = computed(() => props.task.extractionStatus === 'paused');
const canCancel = computed(() => ['pending', 'extracting', 'paused'].includes(props.task.extractionStatus || ''));
const canRetry = computed(() => props.task.extractionStatus === 'error');
const canDelete = computed(() => ['completed', 'error', 'cancelled'].includes(props.task.extractionStatus || ''));

// 方法
const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond === 0) return '0 B/s';
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
  let size = bytesPerSecond;
  let unitIndex = 0;
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`;
};

const formatTime = (seconds: number): string => {
  if (seconds === 0 || !isFinite(seconds)) return '--';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

const formatDateTime = (date: Date): string => {
  return new Intl.DateTimeFormat('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date));
};

const submitPassword = () => {
  if (password.value.trim()) {
    emit('password', props.task.id, password.value.trim());
    password.value = '';
  }
};

const cancelPassword = () => {
  password.value = '';
  emit('password-cancel', props.task.id);
};
</script>

<style scoped>
.extraction-progress-card {
  transition: all 0.2s ease-in-out;
}

.extraction-progress-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
