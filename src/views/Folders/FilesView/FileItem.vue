<template>
  <div
    class="flex flex-col items-center p-4 rounded-lg border border-border transition-all duration-200 ease-[cubic-bezier(0.4,0,0.2,1)] cursor-pointer relative overflow-hidden bg-card will-change-[transform,background-color,border-color] hover:border-primary hover:-translate-y-0.5 hover:shadow-[0_4px_12px_rgba(0,0,0,0.1)] active:translate-y-[-2px] active:duration-100 group"
    :class="{
      'bg-primary/10 border-primary': isSelected,
      'hover:ring-2 hover:ring-blue-200': isPreviewable
    }" :data-id="dataId || item.id" :title="isPreviewable ? `双击预览 ${item.name}` : item.name" @click="handleClick"
    @dblclick="handleDoubleClick" @contextmenu.prevent="handleContextMenu">
    <!-- 选择框 -->
    <div class="absolute top-0 left-1 z-10" v-if="showCheckbox">
      <Checkbox :model-value="isSelected" @click.stop @update:model-value="handleSelect" />
    </div>

    <!-- 预览指示器 -->
    <div v-if="isPreviewable"
      class="absolute top-2 right-2 z-10 p-1 text-white bg-blue-500 rounded-full opacity-0 transition-opacity duration-200 group-hover:opacity-100">
      <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
    </div>

    <!-- 文件预览icon -->
    <div v-if="item.thumbnailMedium"
      class="flex justify-center items-center mb-2 rounded-lg transition-colors duration-200 ease-in-out shrink-0">
      <img class="object-contain h-[100px]" :src="item.thumbnailMedium" :alt="item.name">
    </div>
    <div v-else class="w-[100px] h-[100px] flex justify-center items-center">
      <component :is="getFileTypeIcon(item.type)" class="w-20 h-20" :class="getFileTypeColor(item.type)" />
    </div>

    <!-- 文件名 - 支持重命名 -->
    <div v-if="!renameState.isRenaming.value" class="w-full">
      <Tooltip>
        <TooltipTrigger as-child>
          <div class="px-1 text-sm font-medium text-center truncate" :title="item.name">
            {{ item.name }}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ item.name }}</p>
        </TooltipContent>
      </Tooltip>
    </div>
    <div v-else class="mb-1 w-full">
      <RenameInput v-model="renameState.newName.value" :input-ref="renameState.renameInputRef"
        input-class="flex-1 px-2 py-1 h-auto text-sm text-center" @confirm="handleRenameConfirm"
        @cancel="handleRenameCancel" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useItemRename } from './useRename'
import { Checkbox } from '@/components/ui/checkbox'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip'
import RenameInput from './RenameInput.vue'
import { type FileItemType } from '@/types/files'
import { useFolderConfig } from '@/composables/useFolderConfig'
import { isSupportedFileType } from '@/components/Preview'
import { computed } from 'vue'

// 获取文件icon配置
const { getFileTypeIcon, getFileTypeColor } = useFolderConfig()

// Props
const props = defineProps<{
  item: FileItemType,
  isSelected?: boolean
  showCheckbox?: boolean
  dataId?: string
  isRenaming?: boolean
}>()

// Emits
const emit = defineEmits<{
  click: [item: typeof props.item]
  dblclick: [item: typeof props.item]
  contextmenu: [event: MouseEvent, item: typeof props.item]
  select: [itemId: string, selected: boolean]
  rename: [itemId: string, newName: string]
  renameCancel: [itemId: string]
}>()

// 使用重命名 composable
const renameState = useItemRename(props)

// 判断文件是否支持预览 - 只需要检查是否有preview字段
const isPreviewable = computed(() => {
  return isSupportedFileType(props.item.preview)
})

// 重命名事件处理
const handleRenameConfirm = () => {
  const trimmedName = renameState.newName.value.trim()
  if (trimmedName && trimmedName !== props.item.name) {
    emit('rename', props.item.id, trimmedName)
  } else {
    handleRenameCancel()
  }
  renameState.cancelRename()
}

const handleRenameCancel = () => {
  emit('renameCancel', props.item.id)
  renameState.cancelRename()
}

// 事件处理
const handleClick = () => {
  if (!renameState.isRenaming.value) {
    emit('click', props.item)
  }
}

const handleDoubleClick = () => {
  if (!renameState.isRenaming.value) {
    emit('dblclick', props.item)
  }
}

const handleContextMenu = (event: MouseEvent) => {
  emit('contextmenu', event, props.item)
}

const handleSelect = (selected: boolean | 'indeterminate') => {
  emit('select', props.item.id, selected === true)
}
</script>