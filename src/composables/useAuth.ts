import { ref, computed, onMounted, onUnmounted, readonly } from "vue";
import { useUserStore } from "@/store/user";

/** 认证中心地址 */
const endpoint = import.meta.env.VITE_AUTH_URL;

// 认证状态接口
export interface AuthState {
  isLoggedIn: boolean;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

// 全局认证状态（单例）
const authState = ref<AuthState>({
  isLoggedIn: false,
  token: null,
  isLoading: false,
  error: null,
});

// 控制自动登录的开关
const allowAutoLogin = ref<boolean>(true);
// 标记用户是否正在进行主动登录操作（持久化到sessionStorage）
const isUserInitiatedLogin = ref<boolean>(false);
// 退出登录状态标记（防止重定向循环）
const isLoggingOut = ref<boolean>(false);

// 持久化状态的key
const USER_INITIATED_LOGIN_KEY = "user_initiated_login";

/**
 * 获取用户主动登录状态
 */
const getUserInitiatedLoginState = (): boolean => {
  try {
    return sessionStorage.getItem(USER_INITIATED_LOGIN_KEY) === "true";
  } catch {
    return false;
  }
};

/**
 * 设置用户主动登录状态
 */
const setUserInitiatedLoginState = (value: boolean) => {
  try {
    if (value) {
      sessionStorage.setItem(USER_INITIATED_LOGIN_KEY, "true");
    } else {
      sessionStorage.removeItem(USER_INITIATED_LOGIN_KEY);
    }
    isUserInitiatedLogin.value = value;
  } catch {
    isUserInitiatedLogin.value = value;
  }
};

/**
 * 认证相关的 composable
 */
export function useAuth() {
  // 响应式状态
  const isLoginDialogOpen = ref<boolean>(false);

  // 初始化时恢复用户主动登录状态
  isUserInitiatedLogin.value = getUserInitiatedLoginState();

  // 计算登录 URL（带动态时间戳）
  const loginUrl = computed(() => {
    const timestamp = Date.now();
    return `${endpoint}/?_t=${timestamp}#/login?url=internal://login`;
  });

  // 计算属性（基于全局状态）
  const isLoggedIn = computed(() => authState.value.isLoggedIn);
  const token = computed(() => authState.value.token);
  const isLoading = computed(() => authState.value.isLoading);
  const error = computed(() => authState.value.error);

  /**
   * 设置认证 token
   */
  const setToken = async (newToken: string) => {
    authState.value.token = newToken;
    authState.value.isLoggedIn = true;
    authState.value.error = null;
    localStorage.setItem("auth_token", newToken);

    // 登录成功后重置状态
    setUserInitiatedLoginState(false);
    allowAutoLogin.value = true;

    // 验证token有效性
    try {
      const userStore = useUserStore();
      await userStore.fetchUserInfo(false);

      // 验证成功，确保认证状态正确
      authState.value.isLoggedIn = true;
      authState.value.error = null;
    } catch (error) {
      // 验证失败时清除认证状态
      authState.value.token = null;
      authState.value.isLoggedIn = false;
      authState.value.error = error instanceof Error ? error.message : "登录验证失败";
      localStorage.removeItem("auth_token");

      // 禁用自动登录，防止无限循环
      allowAutoLogin.value = false;

      // 通知主进程清除认证状态
      if (window.electronAPI?.clearAuthState) {
        window.electronAPI.clearAuthState();
      }

      throw error;
    }
  };

  /**
   * 清除认证信息
   */
  const clearAuth = () => {
    authState.value.token = null;
    authState.value.isLoggedIn = false;
    authState.value.error = null;
    authState.value.isLoading = false;
    localStorage.removeItem("auth_token");

    // 通知主进程清除认证状态
    if (window.electronAPI?.clearAuthState) {
      window.electronAPI.clearAuthState();
    }
  };

  /**
   * 设置错误状态
   */
  const setError = (errorMessage: string) => {
    authState.value.error = errorMessage;
  };

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    authState.value.isLoading = loading;
  };

  /**
   * 从 URL 参数中检查 token
   */
  const checkTokenFromUrl = async (isInitialCheck = false) => {
    const urlParams = new URLSearchParams(window.location.search);
    const urlToken = urlParams.get("token");

    if (!urlToken) {
      return false;
    }

    // 判断是否应该接受token
    const shouldAcceptToken =
      allowAutoLogin.value || // 允许自动登录
      isUserInitiatedLogin.value || // 用户主动登录
      (isInitialCheck && !authState.value.isLoggedIn); // 初始检查且未登录

    if (!shouldAcceptToken) {
      return false;
    }

    try {
      await setToken(urlToken);

      // 清除 URL 中的 token 参数
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("token");
      window.history.replaceState({}, "", newUrl.toString());
      return true;
    } catch (error) {
      console.error("URL token 设置失败:", error);
      return false;
    }
  };

  /**
   * 监听 URL 变化以检查 token
   */
  const handleUrlChange = () => {
    checkTokenFromUrl(false).catch((error) => {
      console.error("URL变化时检查token失败:", error);
    });
  };

  /**
   * 处理清除认证状态事件
   */
  const handleClearAuthState = (event: CustomEvent) => {
    const { source } = event.detail;
    console.log(`🧹 收到清除认证状态事件 (${source})`);
    clearAuth();
  };

  /**
   * 初始化认证状态
   */
  const initAuth = () => {
    console.log("🔄 初始化认证状态...", {
      allowAutoLogin: allowAutoLogin.value,
      isUserInitiated: isUserInitiatedLogin.value,
    });

    // 从 localStorage 恢复 token（但不立即设置为已登录状态）
    const savedToken = localStorage.getItem("auth_token");
    if (savedToken) {
      authState.value.token = savedToken;
      // 注意：不立即设置 isLoggedIn = true，需要通过 fetchUserInfo 验证token有效性
      console.log("🔄 已从本地存储恢复token，等待验证");
    }

    // 检查 URL 参数中是否有 token（标记为初始检查）
    checkTokenFromUrl(true).catch((error) => {
      console.error("初始化时检查URL token失败:", error);
    });

    // 添加事件监听器
    window.addEventListener("popstate", handleUrlChange);
    window.addEventListener("clear-auth-state", handleClearAuthState as EventListener);

    // 监听来自主进程的 auth-token 消息（Electron 环境）
    if (window.electronAPI) {
      window.electronAPI.onAuthToken(async (receivedToken: string) => {
        // 判断是否应该接受token
        // 如果当前未登录且没有认证错误，则接受token（支持重新登录）
        const shouldAcceptToken =
          !authState.value.isLoggedIn && // 当前未登录
          !authState.value.error && // 没有认证错误
          (allowAutoLogin.value || isUserInitiatedLogin.value || !authState.value.token); // 允许自动登录或用户主动登录或没有现有token

        if (!shouldAcceptToken) {
          return;
        }

        try {
          await setToken(receivedToken);
        } catch (error) {
          console.error("主进程token设置失败:", error);
        }
      });
    }
  };

  /**
   * 清理资源
   */
  const cleanup = () => {
    window.removeEventListener("popstate", handleUrlChange);
    window.removeEventListener("clear-auth-state", handleClearAuthState as EventListener);
  };

  /**
   * 打开登录对话框
   */
  const openLoginDialog = () => {
    isLoginDialogOpen.value = true;
    setError("");
    setLoading(true);
    console.log("🔓 打开登录对话框");
  };

  /**
   * 关闭登录对话框
   */
  const closeLoginDialog = () => {
    isLoginDialogOpen.value = false;
    setLoading(false);
    // 用户关闭登录对话框时，取消主动登录状态
    setUserInitiatedLoginState(false);
    console.log("🔒 关闭登录对话框");
  };

  /**
   * 手动触发登录
   */
  const login = () => {
    if (authState.value.isLoggedIn) {
      return;
    }

    // 清除之前可能存在的错误状态
    authState.value.error = null;

    // 标记为用户主动登录（持久化）
    setUserInitiatedLoginState(true);
    // 允许接收认证token
    allowAutoLogin.value = true;
    openLoginDialog();
  };

  /**
   * 登出
   */
  const logout = () => {
    // 设置退出登录标记，防止认证拦截器干扰
    isLoggingOut.value = true;
    console.log("🚪 开始退出登录流程...");

    try {
      // 清除主动登录状态（包括持久化状态）
      setUserInitiatedLoginState(false);

      // 清除认证信息
      clearAuth();

      // 清除用户信息
      try {
        const userStore = useUserStore();
        userStore.clearUserInfo();
      } catch (error) {
        console.warn("清除用户信息失败:", error);
      }

      // 清除保存的路由信息
      try {
        sessionStorage.removeItem("cloudDrive_lastRoute");
      } catch (error) {
        console.warn("清除路由信息失败:", error);
      }

      // 通知Electron主进程清除认证状态
      if (window.electronAPI?.clearAuthState) {
        window.electronAPI.clearAuthState();
      }

      console.log("✅ 退出登录流程完成");
    } finally {
      // 延迟清除退出登录标记和重新启用自动登录
      setTimeout(() => {
        isLoggingOut.value = false;
        allowAutoLogin.value = true;
        console.log("🔄 退出登录标记已清除，自动登录已重新启用");
      }, 1000);
    }
  };

  // 生命周期钩子
  onMounted(() => {
    initAuth();
  });

  onUnmounted(() => {
    cleanup();
  });

  return {
    // 状态
    isLoggedIn,
    token,
    isLoading,
    error,
    loginUrl,
    isLoginDialogOpen: computed(() => isLoginDialogOpen.value),

    // 方法
    login,
    logout,
    openLoginDialog,
    closeLoginDialog,
    setToken,
    clearAuth,
    setError,
  };
}

/**
 * 获取全局认证状态（只读）
 */
export function getAuthState(): Readonly<AuthState> {
  return readonly(authState.value);
}

/**
 * 获取当前认证 token（用于 HTTP 拦截器）
 */
export function getAuthToken(): string | null {
  return authState.value.token || localStorage.getItem("auth_token");
}

/**
 * 检查是否已登录
 */
export function isAuthenticated(): boolean {
  // 只依赖应用的认证状态，不再检查localStorage中的token
  // 这样可以避免token失效时的无限循环问题
  return authState.value.isLoggedIn;
}

/**
 * 监听认证状态变化（用于其他 composables）
 */
export function watchAuthState() {
  return readonly(authState);
}

/**
 * 检查是否正在退出登录（用于防止重定向循环）
 */
export function isLoggingOutState(): boolean {
  return isLoggingOut.value;
}
