<template>
  <div ref="containerRef" class="virtual-batch-task-list" :style="{ height: `${height}px`, overflow: 'auto' }">
    <div :style="{ height: `${totalSize}px`, position: 'relative' }">
      <div v-for="virtualItem in virtualItems" :key="virtualItem.key" :style="{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: `${virtualItem.size}px`,
        transform: `translateY(${virtualItem.start}px)`
      }">
        <div class="virtual-item-content">
          <slot :item="flattenedItems[virtualItem.index]" :index="virtualItem.index" :virtualItem="virtualItem"
            :isExpanded="isExpanded" :toggleExpanded="toggleExpanded" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" generic="T">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface BatchTaskItem {
  id: string
  type: 'batch' | 'subtask'
  batchId?: string
  data: T
}

interface VirtualItem {
  key: string | number
  index: number
  start: number
  size: number
}

type ScrollAlign = 'start' | 'center' | 'end' | 'auto'

interface Props {
  batchTasks: T[]
  height: number
  estimateSize?: number
  overscan?: number
  getItemKey?: (item: BatchTaskItem, index: number) => string | number
  getSubTasks?: (batchTask: T) => any[]
  defaultExpanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  estimateSize: 60,
  overscan: 5,
  getItemKey: (item: BatchTaskItem, index: number) => `${item.type}-${item.id}-${index}`,
  getSubTasks: () => [],
  defaultExpanded: false
})

const containerRef = ref<HTMLElement>()

// 虚拟滚动状态
const scrollTop = ref(0)

// 展开状态管理
const expandedBatches = ref<Set<string>>(new Set())

// 存储事件监听器引用以便清理
let scrollEventCleanup: (() => void) | null = null

// 初始化展开状态和事件监听器
onMounted(() => {
  // 初始化展开状态
  if (props.defaultExpanded) {
    props.batchTasks.forEach(task => {
      if (task && typeof task === 'object' && 'id' in task) {
        expandedBatches.value.add((task as any).id)
      }
    })
  }

  // 添加滚动事件监听器
  if (containerRef.value) {
    const element = containerRef.value
    element.addEventListener('scroll', handleScroll, { passive: true })

    // 保存清理函数
    scrollEventCleanup = () => {
      element.removeEventListener('scroll', handleScroll)
    }
  }
})

// 切换展开状态
const toggleExpanded = (batchId: string) => {
  if (expandedBatches.value.has(batchId)) {
    expandedBatches.value.delete(batchId)
  } else {
    expandedBatches.value.add(batchId)
  }
}

// 检查是否展开
const isExpanded = (batchId: string) => {
  return expandedBatches.value.has(batchId)
}

// 将批量任务和子任务扁平化为单一列表
const flattenedItems = computed<BatchTaskItem[]>(() => {
  const result: BatchTaskItem[] = []

  props.batchTasks.forEach(batchTask => {
    if (!batchTask || typeof batchTask !== 'object' || !('id' in batchTask)) {
      return
    }

    const batchId = (batchTask as any).id

    // 添加批量任务本身
    result.push({
      id: batchId,
      type: 'batch',
      data: batchTask
    })

    // 如果展开，添加子任务
    if (expandedBatches.value.has(batchId)) {
      const subTasks = props.getSubTasks(batchTask)
      subTasks.forEach((subTask, index) => {
        result.push({
          id: `${batchId}-sub-${index}`,
          type: 'subtask',
          batchId: batchId,
          data: subTask
        })
      })
    }
  })

  return result
})

// 动态计算项目高度
const getItemSize = (index: number) => {
  const item = flattenedItems.value[index]
  if (!item) return props.estimateSize

  // 批量任务通常比子任务高一些
  if (item.type === 'batch') {
    return props.estimateSize + 10 // 批量任务稍高
  } else {
    return props.estimateSize - 5 // 子任务稍低
  }
}

// 计算总高度
const totalSize = computed(() => {
  let total = 0
  for (let i = 0; i < flattenedItems.value.length; i++) {
    total += getItemSize(i)
  }
  return total
})

// 计算可见区域
const visibleRange = computed(() => {
  const items = flattenedItems.value
  if (items.length === 0) return { start: 0, end: -1 }

  let currentOffset = 0
  let startIndex = 0
  let endIndex = items.length - 1

  // 找到开始索引
  for (let i = 0; i < items.length; i++) {
    const itemSize = getItemSize(i)
    if (currentOffset + itemSize > scrollTop.value) {
      startIndex = i
      break
    }
    currentOffset += itemSize
  }

  // 找到结束索引
  currentOffset = 0
  for (let i = 0; i < items.length; i++) {
    const itemSize = getItemSize(i)
    currentOffset += itemSize
    if (currentOffset >= scrollTop.value + props.height) {
      endIndex = i
      break
    }
  }

  // 添加 overscan
  const overscanStart = Math.max(0, startIndex - props.overscan)
  const overscanEnd = Math.min(items.length - 1, endIndex + props.overscan)

  return {
    start: overscanStart,
    end: overscanEnd
  }
})

// 计算可见项目
const virtualItems = computed<VirtualItem[]>(() => {
  const { start, end } = visibleRange.value
  const items: VirtualItem[] = []

  if (start > end) return items

  let currentOffset = 0

  // 计算到开始位置的偏移量
  for (let i = 0; i < start; i++) {
    currentOffset += getItemSize(i)
  }

  // 生成可见项目
  for (let i = start; i <= end; i++) {
    if (i < flattenedItems.value.length) {
      const size = getItemSize(i)
      items.push({
        key: props.getItemKey(flattenedItems.value[i], i),
        index: i,
        start: currentOffset,
        size
      })
      currentOffset += size
    }
  }

  return items
})

// 处理滚动事件
const handleScroll = (event: Event) => {
  const target = event.target as HTMLElement
  if (target) {
    scrollTop.value = target.scrollTop
  }
}

// 监听扁平化项目变化，虚拟列表会自动响应
watch(() => flattenedItems.value.length, () => {
  // 虚拟列表会自动重新计算可见区域
}, { flush: 'post' })

// 监听展开状态变化，虚拟列表会自动响应
watch(expandedBatches, () => {
  // 虚拟列表会自动重新计算可见区域
}, { deep: true, flush: 'post' })

// 提供滚动到指定项的方法
const scrollToItem = (index: number, options?: { align?: ScrollAlign }) => {
  if (!containerRef.value || index < 0 || index >= flattenedItems.value.length) return

  // 计算到目标项的偏移量
  let targetOffset = 0
  for (let i = 0; i < index; i++) {
    targetOffset += getItemSize(i)
  }

  const itemSize = getItemSize(index)
  let scrollOffset = targetOffset

  if (options?.align === 'center') {
    scrollOffset = targetOffset - (props.height - itemSize) / 2
  } else if (options?.align === 'end') {
    scrollOffset = targetOffset - props.height + itemSize
  }

  scrollOffset = Math.max(0, Math.min(scrollOffset, totalSize.value - props.height))
  containerRef.value.scrollTop = scrollOffset
}

// 提供滚动到顶部的方法
const scrollToTop = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = 0
  }
}

// 提供滚动到底部的方法
const scrollToBottom = () => {
  if (containerRef.value) {
    containerRef.value.scrollTop = totalSize.value
  }
}

// 展开所有批量任务
const expandAll = () => {
  props.batchTasks.forEach(task => {
    if (task && typeof task === 'object' && 'id' in task) {
      expandedBatches.value.add((task as any).id)
    }
  })
}

// 折叠所有批量任务
const collapseAll = () => {
  expandedBatches.value.clear()
}

// 暴露方法给父组件
defineExpose({
  scrollToItem,
  scrollToTop,
  scrollToBottom,
  expandAll,
  collapseAll,
  toggleExpanded,
  isExpanded
})

// 性能优化：在组件卸载时清理
onUnmounted(() => {
  // 清理事件监听器
  if (scrollEventCleanup) {
    scrollEventCleanup()
    scrollEventCleanup = null
  }

  // 清理展开状态
  expandedBatches.value.clear()
})
</script>

<style scoped>
.virtual-batch-task-list {
  /* 确保滚动条样式一致 */
  scrollbar-width: thin;
  scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
}

.virtual-batch-task-list::-webkit-scrollbar {
  width: 6px;
}

.virtual-batch-task-list::-webkit-scrollbar-track {
  background: transparent;
}

.virtual-batch-task-list::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 3px;
}

.virtual-batch-task-list::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.7);
}

.virtual-item-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style>
