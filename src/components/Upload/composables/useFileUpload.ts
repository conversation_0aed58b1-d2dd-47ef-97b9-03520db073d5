import { ref, computed } from "vue";
import { formatFileSize as formatFileSizeUtil } from "@/lib/upload-utils";
import { generateUniqueId } from "@/lib/utils";
import { useUploadStrategy } from "./useUploadStrategy";

export interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  preview?: string;
  progress?: number;
  status: "pending" | "uploading" | "success" | "error";
  error?: string;
  paused?: boolean;
  tusTaskId?: string;
}

export interface UseFileUploadOptions {
  accept?: string;
  multiple?: boolean;
  maxSize?: number; // bytes
  maxFiles?: number;
  allowDirectories?: boolean;
}

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const {
    accept = "",
    multiple = true,
    maxSize = 100 * 1024 * 1024, // 100MB
    maxFiles = Infinity, // 移除文件数量限制
    allowDirectories = true,
  } = options;

  const files = ref<UploadFile[]>([]);
  const isDragging = ref(false);
  const uploadStrategy = useUploadStrategy();

  // 计算属性
  const totalFiles = computed(() => files.value.length);
  const totalSize = computed(() => files.value.reduce((total, file) => total + file.size, 0));
  const canAddMore = computed(() => maxFiles === Infinity || totalFiles.value < maxFiles);

  // 使用共享的文件大小格式化函数
  const formatFileSize = formatFileSizeUtil;

  // 验证文件
  const validateFile = (file: File): string | null => {
    // 检查文件大小
    if (file.size > maxSize) {
      return `文件大小不能超过 ${formatFileSize(maxSize)}`;
    }

    // 检查文件类型
    if (accept && !matchesAcceptPattern(file, accept)) {
      return `不支持的文件类型: ${file.name}`;
    }

    // 检查重复文件
    const isDuplicate = files.value.some((f) => f.name === file.name && f.size === file.size && f.type === file.type);
    if (isDuplicate) {
      return `文件已存在: ${file.name}`;
    }

    return null;
  };

  // 检查文件类型是否匹配 accept 模式
  const matchesAcceptPattern = (file: File, acceptPattern: string): boolean => {
    if (!acceptPattern.trim()) return true;

    const patterns = acceptPattern.split(",").map((p) => p.trim());

    return patterns.some((pattern) => {
      if (pattern.startsWith(".")) {
        // 文件扩展名匹配
        return file.name.toLowerCase().endsWith(pattern.toLowerCase());
      } else if (pattern.includes("*")) {
        // MIME 类型通配符匹配
        const regex = new RegExp(pattern.replace("*", ".*"));
        return regex.test(file.type);
      } else {
        // 精确 MIME 类型匹配
        return file.type === pattern;
      }
    });
  };

  // 创建文件预览（仅小图片类型）
  const createPreview = (file: File): Promise<string | undefined> => {
    return new Promise((resolve) => {
      // 只为小于 10MB 的图片生成预览，避免大文件预览卡死
      const maxPreviewSize = 10 * 1024 * 1024; // 10MB

      if (file.type.startsWith("image/") && file.size <= maxPreviewSize) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.onerror = () => resolve(undefined);
        reader.readAsDataURL(file);
      } else {
        resolve(undefined);
      }
    });
  };

  // 处理文件选择
  const processFiles = async (fileList: FileList | File[]): Promise<string[]> => {
    const newFiles: UploadFile[] = [];
    const errors: string[] = [];

    const fileArray = Array.from(fileList);
    console.log(`📁 processFiles: 开始处理 ${fileArray.length} 个文件`);

    // 调试：打印前几个文件的信息
    fileArray.slice(0, 5).forEach((file, index) => {
      const relativePath = (file as any).webkitRelativePath || file.name;
      console.log(`📁 文件 ${index + 1}: ${relativePath} (${file.size} bytes)`);
    });

    // 文件去重：基于文件路径和大小进行去重
    const uniqueFiles = new Map<string, File>();

    fileArray.forEach((file) => {
      // 使用webkitRelativePath（如果存在）或文件名 + 大小作为唯一标识
      const relativePath = (file as any).webkitRelativePath || file.name;
      const uniqueKey = `${relativePath}_${file.size}_${file.lastModified}`;

      if (!uniqueFiles.has(uniqueKey)) {
        uniqueFiles.set(uniqueKey, file);
      } else {
        console.log(`📁 发现重复文件: ${relativePath}`);
      }
    });

    const deduplicatedFiles = Array.from(uniqueFiles.values());
    console.log(`📁 文件去重: 原始 ${fileArray.length} 个文件，去重后 ${deduplicatedFiles.length} 个文件`);

    // 如果有文件数量限制，进行检查
    let filesToProcess = deduplicatedFiles;
    if (maxFiles !== Infinity) {
      const remainingSlots = maxFiles - files.value.length;

      // 如果没有剩余空间，直接返回错误
      if (remainingSlots <= 0) {
        errors.push(`已达到最大文件数量限制 (${maxFiles})，无法添加更多文件`);
        return errors;
      }

      // 如果选择的文件数超过剩余空间，只处理能容纳的文件数
      if (deduplicatedFiles.length > remainingSlots) {
        filesToProcess = deduplicatedFiles.slice(0, remainingSlots);
        errors.push(`只能再添加 ${remainingSlots} 个文件，已自动选择前 ${remainingSlots} 个文件`);
      }
    }

    // 检查是否已存在相同的文件（基于现有文件列表）
    const existingFileKeys = new Set(
      files.value.map((f) => {
        const relativePath = (f.file as any).webkitRelativePath || f.file.name;
        return `${relativePath}_${f.file.size}_${f.file.lastModified}`;
      })
    );

    for (const file of filesToProcess) {
      // 检查文件是否已存在
      const relativePath = (file as any).webkitRelativePath || file.name;
      const fileKey = `${relativePath}_${file.size}_${file.lastModified}`;

      if (existingFileKeys.has(fileKey)) {
        console.log(`⚠️ 跳过重复文件: ${relativePath}`);
        continue;
      }

      // 验证文件
      const error = validateFile(file);
      if (error) {
        errors.push(error);
        continue;
      }

      // 创建上传文件对象
      const uploadFile: UploadFile = {
        id: generateUniqueId("file"),
        file,
        name: file.name,
        size: file.size,
        type: file.type || "application/octet-stream",
        status: "pending",
      };

      // 创建预览
      uploadFile.preview = await createPreview(file);
      newFiles.push(uploadFile);
    }

    // 添加新文件
    files.value.push(...newFiles);
    console.log(`📁 最终添加 ${newFiles.length} 个新文件，当前总计 ${files.value.length} 个文件`);
    return errors;
  };

  // 处理文件输入事件
  const handleFileInput = async (event: Event): Promise<string[]> => {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      return [];
    }

    console.log(`📁 handleFileInput: 接收到 ${input.files.length} 个文件`);
    console.log(`📁 当前已有文件数量: ${files.value.length}`);

    const errors = await processFiles(input.files);

    // 清空 input 以允许重复选择同一文件
    input.value = "";

    return errors;
  };

  // 处理拖拽事件
  const handleDragOver = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();
    isDragging.value = true;
  };

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault();
    event.stopPropagation();

    // 检查是否真正离开了拖拽区域
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const { clientX: x, clientY: y } = event;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      isDragging.value = false;
    }
  };

  const handleDrop = async (event: DragEvent): Promise<{ errors: string[] }> => {
    event.preventDefault();
    event.stopPropagation();
    isDragging.value = false;

    const droppedFiles = await processDroppedItems(event.dataTransfer?.items);
    const processErrors = await processFiles(droppedFiles);

    return {
      errors: processErrors,
    };
  };

  // 处理拖拽的文件和目录
  const processDroppedItems = async (items?: DataTransferItemList): Promise<File[]> => {
    if (!items) return [];

    const files: File[] = [];

    const promises = Array.from(items).map(async (item) => {
      if (item.kind !== "file") return;

      const entry = item.webkitGetAsEntry();
      if (!entry) return;

      if (entry.isFile) {
        const file = item.getAsFile();
        if (file) {
          files.push(file);
        }
      } else if (entry.isDirectory) {
        if (allowDirectories) {
          try {
            const dirFiles = await processDirectory(entry as FileSystemDirectoryEntry, entry.name);
            files.push(...dirFiles);
          } catch (error) {
            console.error(`处理目录 "${entry.name}" 时出错:`, error);
          }
        }
      }
    });

    await Promise.all(promises);
    return files;
  };

  // 递归处理目录
  const processDirectory = async (dirEntry: FileSystemDirectoryEntry, basePath: string = ""): Promise<File[]> => {
    return new Promise((resolve, reject) => {
      const files: File[] = [];
      const reader = dirEntry.createReader();

      const readEntries = () => {
        reader.readEntries(async (entries) => {
          if (entries.length === 0) {
            resolve(files);
            return;
          }

          const promises = entries.map(async (entry) => {
            const currentPath = basePath ? `${basePath}/${entry.name}` : entry.name;

            if (entry.isFile) {
              try {
                const file = await new Promise<File>((resolve, reject) => {
                  (entry as FileSystemFileEntry).file(resolve, reject);
                });

                const fileWithPath = new File([file], file.name, {
                  type: file.type,
                  lastModified: file.lastModified,
                });

                Object.defineProperty(fileWithPath, "webkitRelativePath", {
                  value: currentPath,
                  writable: false,
                  enumerable: true,
                  configurable: false,
                });

                files.push(fileWithPath);
              } catch (error) {
                console.warn(`无法读取文件: ${entry.name}`);
              }
            } else if (entry.isDirectory) {
              try {
                const subFiles = await processDirectory(entry as FileSystemDirectoryEntry, currentPath);
                files.push(...subFiles);
              } catch (error) {
                console.warn(`无法读取目录: ${entry.name}`);
              }
            }
          });

          await Promise.all(promises);
          readEntries();
        }, reject);
      };

      readEntries();
    });
  };

  // 启动智能上传
  const startUpload = async (attributes?: Record<string, string>) => {
    if (files.value.length === 0) {
      return;
    }

    try {
      const filesToUpload = files.value.map((f) => f.file);
      await uploadStrategy.uploadFiles(filesToUpload, attributes);

      // 上传成功后清空文件列表
      clearFiles();
    } catch (error) {
      console.error("上传失败:", error);
      throw error;
    }
  };

  // 移除文件
  const removeFile = (fileId: string) => {
    const index = files.value.findIndex((f) => f.id === fileId);
    if (index > -1) {
      files.value.splice(index, 1);
    }
  };

  // 清空所有文件
  const clearFiles = () => {
    files.value = [];
  };

  // 更新文件状态
  const updateFileStatus = (fileId: string, status: UploadFile["status"], progress?: number, error?: string) => {
    const file = files.value.find((f) => f.id === fileId);
    if (file) {
      file.status = status;
      if (progress !== undefined) file.progress = progress;
      if (error !== undefined) file.error = error;
    }
  };

  return {
    // 状态
    files,
    isDragging,
    totalFiles,
    totalSize,
    canAddMore,

    // 方法
    handleFileInput,
    processFiles,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    removeFile,
    clearFiles,
    updateFileStatus,
    formatFileSize,
    startUpload,

    // 配置
    accept,
    multiple,
    maxSize,
    maxFiles,
    allowDirectories,
  };
}
