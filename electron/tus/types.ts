// TUS 上传相关类型定义

// 上传任务状态
export type UploadStatus = "pending" | "uploading" | "paused" | "completed" | "error" | "cancelled";

// 上传任务接口
export interface UploadTask {
  id: string;
  filePath: string;
  fileName: string;
  fileSize: number;
  uploadUrl?: string;
  progress: number;
  status: UploadStatus;
  bytesUploaded: number;
  uploadSpeed: number;
  remainingTime: number;
  startTime: Date;
  error?: string;
  metadata?: Record<string, string>;
  resumable: boolean;
}

// 上传配置接口
export interface TusUploadConfig {
  endpoint: string;
  chunkSize?: number;
  retryDelays?: number[];
  parallelUploads?: number;
  metadata?: Record<string, string>;
  headers?: Record<string, string>;
}

// 上传事件接口
export interface UploadEvents {
  "task-created": (taskId: string, task: UploadTask) => void;
  "task-progress": (taskId: string, progress: number, bytesUploaded: number, bytesTotal: number) => void;
  "task-status-changed": (taskId: string, status: UploadStatus, error?: string) => void;
  "task-completed": (taskId: string) => void;
  "task-error": (taskId: string, error: string) => void;
}

// API 响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: UploadTask;
  tasks?: UploadTask[];
  data?: T;
}

// 存储数据接口
export interface StoreData {
  tasks: Record<string, UploadTask>;
  settings: {
    chunkSize: number;
    retryDelays: number[];
    parallelUploads: number;
  };
}
