<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>选择下载保存位置</DialogTitle>
        <DialogDescription>
          请选择文件的保存位置
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- 当前选择的路径显示 -->
        <div class="space-y-2">
          <Label for="selected-path">保存路径</Label>
          <div class="flex gap-2">
            <Input id="selected-path" :value="selectedPath" readonly placeholder="请选择保存路径..." class="flex-1" />
            <Button @click="handleBrowse" :disabled="isBrowsing">
              <FolderOpen class="mr-2 w-4 h-4" />
              {{ isBrowsing ? '选择中...' : '浏览' }}
            </Button>
          </div>
        </div>

        <!-- 下载信息显示 -->
        <div v-if="downloadInfo" class="p-3 rounded-lg bg-muted">
          <div class="mb-1 text-sm text-muted-foreground">下载内容</div>
          <div class="font-medium">{{ downloadInfo.name }}</div>
          <div v-if="downloadInfo.count && downloadInfo.count > 1" class="text-sm text-muted-foreground">
            共 {{ downloadInfo.count }} 个项目
          </div>
          <div v-if="downloadInfo.size" class="text-sm text-muted-foreground">
            大小: {{ downloadInfo.size }}
          </div>
        </div>

        <!-- 记住选择选项(先隐藏) -->
        <div v-if="false" class="flex items-center space-x-2">
          <Checkbox id="remember-path" :checked="rememberPath" @update:checked="rememberPath = $event" />
          <Label for="remember-path" class="text-sm">
            记住此路径作为默认下载位置
          </Label>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="handleCancel">
          取消
        </Button>
        <Button @click="handleConfirm" :disabled="!selectedPath || isConfirming">
          {{ isConfirming ? '确认中...' : '开始下载' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { FolderOpen } from 'lucide-vue-next'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { toast } from 'vue-sonner'

// Props
const props = defineProps<{
  open: boolean
  downloadInfo?: {
    name: string
    count?: number
    size?: string
  }
}>()

// Emits
const emit = defineEmits<{
  'update:open': [value: boolean]
  confirm: [path: string, remember: boolean]
  cancel: []
}>()

// 状态
const selectedPath = ref('')
const rememberPath = ref(false)
const isBrowsing = ref(false)
const isConfirming = ref(false)

// 处理对话框开关
const handleOpenChange = (value: boolean) => {
  emit('update:open', value)
  if (!value) {
    handleCancel()
  }
}

// 获取默认下载路径
const getDefaultDownloadPath = async (): Promise<string> => {
  try {
    const api = (window as any).electronAPI
    if (api?.download?.getDefaultDownloadPath) {
      const result = await api.download.getDefaultDownloadPath()
      if (result.success && result.data?.path) {
        return result.data.path
      }
    }
  } catch (error) {
    console.warn('获取默认下载路径失败:', error)
  }
  return ''
}

// 获取记住的下载路径
const getRememberedPath = (): string => {
  return localStorage.getItem('download-remembered-path') || ''
}

// 保存记住的路径
const saveRememberedPath = (path: string) => {
  if (path) {
    localStorage.setItem('download-remembered-path', path)
  }
}

// 清除记住的路径
const clearRememberedPath = () => {
  localStorage.removeItem('download-remembered-path')
}

// 初始化路径
const initializePath = async () => {
  // 优先使用记住的路径
  const rememberedPath = getRememberedPath()
  if (rememberedPath) {
    selectedPath.value = rememberedPath
    rememberPath.value = true
    return
  }

  // 否则使用默认路径
  const defaultPath = await getDefaultDownloadPath()
  if (defaultPath) {
    selectedPath.value = defaultPath
  }
}

// 浏览文件夹
const handleBrowse = async () => {
  if (isBrowsing.value) return

  try {
    isBrowsing.value = true
    const api = (window as any).electronAPI

    if (!api?.download?.showSelectFolderDialog) {
      toast.error('文件夹选择功能不可用')
      return
    }

    const result = await api.download.showSelectFolderDialog(selectedPath.value)

    if (result.success && result.data?.path) {
      selectedPath.value = result.data.path
    }
  } catch (error) {
    console.error('浏览文件夹失败:', error)
    toast.error('选择文件夹时发生错误')
  } finally {
    isBrowsing.value = false
  }
}

// 确认下载
const handleConfirm = async () => {
  if (!selectedPath.value || isConfirming.value) return

  try {
    isConfirming.value = true

    // 保存路径记忆
    if (rememberPath.value) {
      saveRememberedPath(selectedPath.value)
    } else {
      clearRememberedPath()
    }

    emit('confirm', selectedPath.value, rememberPath.value)
  } catch (error) {
    console.error('确认下载失败:', error)
    toast.error('确认下载时发生错误')
  } finally {
    isConfirming.value = false
  }
}

// 取消下载
const handleCancel = () => {
  selectedPath.value = ''
  rememberPath.value = false
  emit('cancel')
}

// 监听对话框打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    initializePath()
  } else {
    // 重置状态
    selectedPath.value = ''
    rememberPath.value = false
    isBrowsing.value = false
    isConfirming.value = false
  }
})

// 组件挂载时初始化
onMounted(() => {
  if (props.open) {
    initializePath()
  }
})
</script>
