<template>
  <div class="space-y-4">
    <!-- 工具栏 -->
    <div class="flex justify-between items-center">
      <div class="flex gap-4 items-center">
        <!-- 搜索框 -->
        <div class="relative w-64">
          <Search class="absolute left-3 top-1/2 w-4 h-4 transform -translate-y-1/2 text-muted-foreground" />
          <Input v-model="searchQuery" placeholder="搜索标签..." class="pl-10" />
        </div>

        <!-- 选择信息 -->
        <div v-if="hasSelection" class="flex gap-2 items-center">
          <span class="text-sm text-muted-foreground">已选择 {{ selectedCount }} 项</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex gap-2 items-center">
        <Button variant="outline" size="sm" @click="refresh" :disabled="state.loading">
          <RefreshCw class="mr-1 w-4 h-4" :class="{ 'animate-spin': state.loading }" />
          刷新
        </Button>
        <!-- 操作按钮已移除，因为只能编辑现有类别的标签 -->
      </div>
    </div>

    <!-- 表格 -->
    <div class="overflow-hidden rounded-lg border">
      <div class="overflow-auto max-h-[calc(100vh-250px)]">
        <div class="min-w-full">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="text-center w-22">类别名称</TableHead>
                <TableHead class="text-center">标签列表</TableHead>
                <TableHead class="text-center">更新人</TableHead>
                <TableHead class="text-center">更新时间</TableHead>
                <TableHead class="w-20 text-center">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-if="state.loading">
                <TableCell colspan="6" class="py-8 text-center">
                  <div class="flex justify-center items-center">
                    <RefreshCw class="mr-2 w-4 h-4 animate-spin" />
                    加载中...
                  </div>
                </TableCell>
              </TableRow>
              <TableRow v-else-if="filteredCategories.length === 0">
                <TableCell colspan="6" class="py-8 text-center text-muted-foreground">
                  {{ state.searchQuery ? '没有找到匹配的类别' : '暂无数据' }}
                </TableCell>
              </TableRow>
              <TableRow v-else class="text-center" v-for="category in filteredCategories" :key="category.category_id">
                <TableCell>{{ category.category_name }}</TableCell>
                <TableCell class="min-w-[200px] max-w-md">
                  <div class="relative">
                    <div
                      class="flex overflow-y-auto overflow-x-hidden flex-wrap gap-1 p-2 rounded-md border border-gray-200 transition-all duration-200 max-h-26 tag-scroll-container bg-gray-50/50 hover:border-gray-300 hover:bg-gray-100/50">
                      <span v-for="tag in category.tags" :key="tag.id"
                        class="inline-flex items-center px-2 py-1 text-xs text-[#409EFF] whitespace-nowrap border border-[#B3D8FF] bg-[#ECF5FF] rounded-sm transition-colors">
                        {{ tag.item_value }}
                      </span>
                      <span v-if="category.tags.length === 0" class="text-xs text-muted-foreground">
                        暂无标签
                      </span>
                    </div>
                    <!-- 滚动提示和标签计数 -->
                    <div v-if="category.tags.length > 0" class="flex absolute -top-1 -right-1 items-center">
                      <span
                        class="px-1.5 py-0.5 text-xs text-gray-500 bg-white rounded-full border border-gray-200 shadow-sm">
                        {{ category.tags.length }}
                      </span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>{{ category.update_user }}</TableCell>
                <TableCell>{{ formatDate(category.update_time) }}</TableCell>
                <TableCell>
                  <div class="flex gap-2 items-center">
                    <Button variant="ghost" size="sm" @click="handleEditCategory(category)">
                      <Edit class="w-4 h-4" />
                      编辑
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>

    <!-- 编辑类别对话框 -->
    <TagCategoryEditDialog v-model:open="isEditDialogOpen" :category-data="state.editingCategory"
      @submit="handleEditTag" />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { Search, Edit, RefreshCw } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { useTagsManagement } from './composables/useTagsManagement'
import { useSearch } from '@/composables/useSearch'
import TagCategoryEditDialog from './TagCategoryEditDialog.vue'
import type { EditTagParams } from '@/api/services/tags'

// 使用标签管理 composable
const {
  state,
  isEditDialogOpen,
  hasSelection,
  selectedCount,
  filteredCategories,
  fetchTags,
  editTag,
  search,
  openEditDialog,
  refresh,
} = useTagsManagement()

// 使用统一搜索 composable
const { searchQuery, handleSearchInput } = useSearch({
  debounceDelay: 300,
  onSearch: (query: string) => {
    search(query)
  }
})

// 监听搜索查询变化，处理中文输入法问题
watch(searchQuery, (newQuery) => {
  handleSearchInput(newQuery)
}, { immediate: false })

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 事件处理
const handleEditCategory = (category: any) => {
  openEditDialog(category)
}

const handleEditTag = async (data: EditTagParams) => {
  await editTag(data)
}

// 生命周期
onMounted(() => {
  fetchTags()
})

// 搜索功能的定时器清理现在由 useSearch composable 自动处理
</script>

<style scoped>
/* 自定义滚动条样式 */
.tag-scroll-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.tag-scroll-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.tag-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tag-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox 滚动条样式 */
.tag-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}
</style>
