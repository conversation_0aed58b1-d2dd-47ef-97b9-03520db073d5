<template>
  <div class="list-view">
    <Table>
      <!-- 表头 -->
      <TableHeader>
        <TableRow>
          <TableHead class="w-16">
            <div class="flex gap-2 items-center">
              <Checkbox :model-value="checkboxState" @update:model-value="toggleSelectAll" />
              <span class="hidden text-xs text-muted-foreground sm:inline">
                {{ selectedItems.size > 0 ? `${selectedItems.size}/${items.length}` : '' }}
              </span>
            </div>
          </TableHead>
          <TableHead class="min-w-0">
            <div class="flex items-center">
              <!-- 排序按钮 -->
              <Button variant="ghost" size="sm" class="justify-start p-0 h-auto font-medium hover:bg-transparent"
                @click="handleSort('name')">
                文件名
                <ChevronUp v-if="sortBy === 'name' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
                <ChevronDown v-else-if="sortBy === 'name' && sortOrder === 'desc'" class="ml-1 w-4 h-4" />
                <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
              </Button>

              <!-- 操作按钮组 -->
              <ActionButtonGroup :selected-count="selectedItems.size" :is-downloading="isDownloading"
                :selected-items="selectedItemsArray" container-class="gap-1 ml-10" @download="handleDownload"
                @move="handleMove" @delete="handleDelete" @delete-success="handleDeleteSuccess"
                @refresh-directory="handleRefreshDirectory" />
            </div>
          </TableHead>
          <TableHead class="hidden w-32 md:table-cell">
            <Button variant="ghost" size="sm" class="justify-start p-0 h-auto font-medium hover:bg-transparent"
              @click="handleSort('uploader')">
              上传者
              <ChevronUp v-if="sortBy === 'uploader' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
              <ChevronDown v-else-if="sortBy === 'uploader' && sortOrder === 'desc'" class="ml-1 w-4 h-4" />
              <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
            </Button>
          </TableHead>
          <TableHead class="hidden w-24 sm:table-cell">
            <Button variant="ghost" size="sm" class="justify-start p-0 h-auto font-medium hover:bg-transparent"
              @click="handleSort('size')">
              大小
              <ChevronUp v-if="sortBy === 'size' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
              <ChevronDown v-else-if="sortBy === 'size' && sortOrder === 'desc'" class="ml-1 w-4 h-4" />
              <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
            </Button>
          </TableHead>
          <TableHead class="w-36">
            <Button variant="ghost" size="sm" class="justify-start p-0 h-auto font-medium hover:bg-transparent"
              @click="handleSort('modifiedAt')">
              修改时间
              <ChevronUp v-if="sortBy === 'modifiedAt' && sortOrder === 'asc'" class="ml-1 w-4 h-4" />
              <ChevronDown v-else-if="sortBy === 'modifiedAt' && sortOrder === 'desc'" class="ml-1 w-4 h-4" />
              <ChevronsUpDown v-else class="ml-1 w-4 h-4 opacity-50" />
            </Button>
          </TableHead>
        </TableRow>
      </TableHeader>

      <!-- 表格内容 -->
      <TableBody>
        <TableRow v-for="item in sortedItems" :key="item.id" :data-id="item.id"
          :class="{ 'bg-muted/50': selectedItems.has(item.id) }"
          class="transition-colors cursor-pointer hover:bg-muted/30" @click="handleItemClick(item)"
          @dblclick="handleItemDoubleClick(item)" @contextmenu.prevent="handleItemContextMenu($event, item)">
          <!-- 多选框 -->
          <TableCell class="w-12">
            <Checkbox :model-value="selectedItems.has(item.id)" @click.stop
              @update:model-value="(value: boolean | 'indeterminate') => toggleItemSelect(item.id, value === true)" />
          </TableCell>

          <!-- 文件名（带图标） -->
          <TableCell class="min-w-0">
            <div class="flex gap-3 items-center">
              <div class="file-icon">
                <Folder v-if="item.type === 'folder'" class="w-4 h-4 text-blue-500" />
                <img v-else-if="item.thumbnailMedium" class="h-full" :src="item.thumbnailMedium" :alt="item.name">
                <component v-else :is="getFileTypeIcon(item.type)" class="w-4 h-4"
                  :class="getFileTypeColor(item.type)" />
              </div>
              <!-- 文件名 - 支持重命名 -->
              <div v-if="renamingItemId !== item.id" class="flex-1 min-w-0">
                <span class="font-medium truncate">{{ item.name }}</span>
              </div>
              <div v-else class="flex-1 min-w-0" @click.stop>
                <RenameInput v-model="newName" :input-ref="renameInputRef" input-class="flex-1 px-2 py-1 h-auto text-sm"
                  @confirm="handleRenameConfirm" @cancel="handleRenameCancel" />
              </div>
            </div>
          </TableCell>

          <!-- 上传者 -->
          <TableCell class="hidden w-32 md:table-cell text-muted-foreground">
            <span class="truncate">{{ getUploader(item) }}</span>
          </TableCell>

          <!-- 大小 -->
          <TableCell class="hidden w-24 sm:table-cell text-muted-foreground">
            <span class="text-sm">
              <template v-if="item.type === 'folder'">
                /
              </template>
              <template v-else>
                {{ getFileSize(item as FileItemType) }}
              </template>
            </span>
          </TableCell>

          <!-- 修改时间 -->
          <TableCell class="w-36 text-muted-foreground">
            <span class="text-sm">{{ formatDateTime(item.modifiedAt) }}</span>
          </TableCell>
        </TableRow>

        <!-- 空状态 -->
        <TableEmpty v-if="items.length === 0" :colspan="5">
          <div class="flex flex-col gap-2 items-center text-muted-foreground">
            <Folder class="w-8 h-8 opacity-50" />
            <p>暂无文件</p>
          </div>
        </TableEmpty>
      </TableBody>
    </Table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Folder, ChevronUp, ChevronDown, ChevronsUpDown } from 'lucide-vue-next'
import { useListViewRename } from './useRename'
import ActionButtonGroup from './ActionButtonGroup.vue'
import RenameInput from './RenameInput.vue'
import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
  TableEmpty
} from '@/components/ui/table'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import type { FileItemType, FolderItemType, ItemType } from '@/types/files'
import { useFolderConfig } from '@/composables/useFolderConfig'

type SortField = 'name' | 'uploader' | 'size' | 'modifiedAt'
type SortOrder = 'asc' | 'desc'

// Props
const props = defineProps<{
  items: ItemType[]
  selectedItems?: Set<string>
  isDownloading?: boolean
}>()

// 获取文件icon配置
const { getFileTypeIcon, getFileTypeColor } = useFolderConfig()

// Emits
const emit = defineEmits<{
  fileClick: [item: FileItemType]
  fileDoubleClick: [item: FileItemType]
  folderClick: [item: FolderItemType]
  folderDoubleClick: [item: FolderItemType]
  contextmenu: [event: MouseEvent, item: ItemType]
  selectionChange: [selectedIds: Set<string>]
  download: [selectedIds: Set<string>]
  move: [selectedIds: Set<string>]
  delete: [selectedIds: Set<string>]
  rename: [itemId: string, newName: string, item: ItemType]
  refreshDirectory: []
}>()

// 排序状态
const sortBy = ref<SortField>('name')
const sortOrder = ref<SortOrder>('asc')

// 选择状态 - 使用父组件传入的选择状态或本地状态
const selectedItems = computed(() => props.selectedItems || new Set<string>())

// 计算选中的项目数组
const selectedItemsArray = computed(() => {
  return props.items.filter(item => selectedItems.value.has(item.id))
})

// 重命名 composable
const {
  renamingItemId,
  newName,
  renameInputRef,
  startRename: startItemRename,
  cancelRename
} = useListViewRename()

// 监听 items 变化，清空选择状态和重命名状态
watch(
  () => props.items,
  () => {
    // 当目录跳转时清空选择状态
    if (selectedItems.value.size > 0) {
      emit('selectionChange', new Set())
    }
    renamingItemId.value = null
  },
  { deep: true }
)

// 排序（名字/上传者/大小/修改时间）
const sortedItems = computed(() => {
  const items_copy = [...props.items]

  return items_copy.sort((a, b) => {
    let compareValue = 0

    switch (sortBy.value) {
      case 'name':
        compareValue = a.name.localeCompare(b.name, 'zh-CN')
        break
      case 'uploader':
        const uploaderA = getUploader(a)
        const uploaderB = getUploader(b)
        compareValue = uploaderA.localeCompare(uploaderB, 'zh-CN')
        break
      case 'size':
        if (a.type === 'folder' && b.type === 'folder') {
          // 都是文件夹，不用移动
          compareValue = 0
        } else if (a.type === 'folder') {
          compareValue = -1 // 文件夹排在前面
        } else if (b.type === 'folder') {
          compareValue = 1
        } else {
          // 简单的文件大小比较（这里需要更复杂的逻辑来解析大小字符串）
          const sizeA = parseFileSize(a.size)
          const sizeB = parseFileSize(b.size)
          compareValue = sizeA - sizeB
        }
        break
      case 'modifiedAt':
        compareValue = a.modifiedAt.getTime() - b.modifiedAt.getTime()
        break
    }

    return sortOrder.value === 'asc' ? compareValue : -compareValue
  })
})

// 计算 Checkbox 的三种状态
const checkboxState = computed<boolean | 'indeterminate'>(() => {
  if (props.items.length === 0) return false
  if (selectedItems.value.size === 0) return false
  if (selectedItems.value.size === props.items.length) return true
  return 'indeterminate'
})

// 方法
const handleSort = (field: SortField) => {
  if (sortBy.value === field) {
    sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value = field
    sortOrder.value = 'asc'
  }
}

const toggleSelectAll = (value: boolean | 'indeterminate') => {
  // 处理三种状态的切换逻辑：
  // false -> true (全选)
  // true -> false (取消全选) 
  // indeterminate -> true (全选)
  if (value === true || value === 'indeterminate') {
    emit('selectionChange', new Set(props.items.map(item => item.id)))
  } else {
    emit('selectionChange', new Set())
  }
}

const toggleItemSelect = (itemId: string, checked: boolean) => {
  const newSelection = new Set(selectedItems.value)
  if (checked) {
    newSelection.add(itemId)
  } else {
    newSelection.delete(itemId)
  }
  emit('selectionChange', newSelection)
}

// 重命名处理
const startRename = (itemId: string) => {
  const item = props.items.find(item => item.id === itemId)
  if (item) {
    startItemRename(itemId, item.name)
  }
}

const handleRenameCancel = () => {
  cancelRename()
}

const handleRenameConfirm = () => {
  if (renamingItemId.value) {
    const trimmedName = newName.value.trim()
    const item = props.items.find(item => item.id === renamingItemId.value)
    if (item && trimmedName && trimmedName !== item.name) {
      emit('rename', renamingItemId.value, trimmedName, item)
    }
    renamingItemId.value = null
  }
}

// 操作按钮处理函数
const handleDownload = () => {
  emit('download', new Set(selectedItems.value))
}

const handleMove = () => {
  emit('move', new Set(selectedItems.value))
}

const handleDelete = () => {
  emit('delete', new Set(selectedItems.value))
}

const handleDeleteSuccess = (_deletedItems: ItemType[]) => {
  // 清空选择状态
  emit('selectionChange', new Set())
}

const handleRefreshDirectory = () => {
  emit('refreshDirectory')
}

const handleItemClick = (item: ItemType) => {
  if (renamingItemId.value) return // 如果正在重命名，不响应点击

  if (item.type === 'folder') {
    emit('folderClick', item as FolderItemType)
  } else {
    emit('fileClick', item as FileItemType)
  }
}

const handleItemDoubleClick = (item: ItemType) => {
  if (renamingItemId.value) return // 如果正在重命名，不响应双击

  if (item.type === 'folder') {
    emit('folderDoubleClick', item as FolderItemType)
  } else {
    emit('fileDoubleClick', item as FileItemType)
  }
}

const handleItemContextMenu = (event: MouseEvent, item: ItemType) => {
  emit('contextmenu', event, item)
}

const getUploader = (item: ItemType): string => {
  return item.uploader || '系统'
}

const getFileSize = (item: FileItemType): string => {
  // 优先使用后端提供的人类可读格式（确保不是空字符串）
  if (item.size_human && item.size_human.trim() !== '') {
    return item.size_human
  }
  // 回退到原有的格式化大小
  return item.size || '未知'
}

const formatDateTime = (date: Date): string => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const parseFileSize = (sizeStr: string): number => {
  const units = { 'B': 1, 'KB': 1024, 'MB': 1024 * 1024, 'GB': 1024 * 1024 * 1024 }
  const match = sizeStr.match(/^([\d.]+)\s*([A-Z]*B?)$/i)
  if (!match) return 0

  const size = parseFloat(match[1])
  const unit = match[2].toUpperCase() as keyof typeof units
  return size * (units[unit] || 1)
}

// 暴露方法给父组件
defineExpose({
  startRename
})
</script>

<style scoped>
.list-view {
  height: 100%;
  overflow: auto;
}

/* 文件图标样式 */
.file-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  height: 40px;
  flex-shrink: 0;
}

/* 自定义选中行样式 */
.list-view :deep(.bg-muted\/50) {
  background-color: hsl(var(--primary) / 0.05);
}

.list-view :deep(.hover\:bg-muted\/30:hover) {
  background-color: hsl(var(--muted) / 0.3);
}

/* 表格行的过渡效果 */
.list-view :deep(tr) {
  transition: background-color 0.15s ease;
}

/* 响应式隐藏样式增强 */
@media (max-width: 768px) {
  .list-view :deep(.hidden.md\:table-cell) {
    display: none !important;
  }
}

@media (max-width: 640px) {
  .list-view :deep(.hidden.sm\:table-cell) {
    display: none !important;
  }
}
</style>