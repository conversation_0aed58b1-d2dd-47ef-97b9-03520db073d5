import { ref, watch } from "vue";

type ViewMode = "grid" | "list";

const VIEW_MODE_STORAGE_KEY = "cloud-drive-view-mode";

// 从 localStorage 读取保存的视图模式，默认为 'list'
const getStoredViewMode = (): ViewMode => {
  try {
    const stored = localStorage.getItem(VIEW_MODE_STORAGE_KEY);
    return stored === "grid" || stored === "list" ? stored : "list";
  } catch (error) {
    console.warn("无法读取视图模式设置:", error);
    return "list";
  }
};

// 保存视图模式到 localStorage
const saveViewMode = (mode: ViewMode) => {
  try {
    localStorage.setItem(VIEW_MODE_STORAGE_KEY, mode);
  } catch (error) {
    console.warn("无法保存视图模式设置:", error);
  }
};

// 全局共享的视图模式状态
const globalViewMode = ref<ViewMode>(getStoredViewMode());

// 监听视图模式变化，自动保存到 localStorage
watch(
  globalViewMode,
  (newMode) => {
    saveViewMode(newMode);
  },
  { immediate: false }
);

/**
 * 视图模式管理 composable
 * 提供视图模式的持久化存储和状态管理
 * 使用全局单例模式，确保所有组件共享同一个状态
 */
export function useViewMode() {
  // 切换视图模式
  const setViewMode = (mode: ViewMode) => {
    globalViewMode.value = mode;
  };

  return {
    viewMode: globalViewMode,
    setViewMode,
  };
}
