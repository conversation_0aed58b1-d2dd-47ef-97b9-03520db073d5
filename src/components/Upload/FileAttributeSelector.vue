<template>
  <div v-if="visibleAttributeSelectors.length > 0" class="attribute-selectors">
    <div class="attribute-grid">
      <div v-for="selector in visibleAttributeSelectors" :key="selector.field_name" class="attribute-item">
        <label :for="selector.field_name" class="attribute-label">
          {{ selector.label }}
        </label>

        <!-- 单选下拉框 -->
        <Select v-if="selector.upload_type === 'select'" :model-value="modelValue[selector.field_name]"
          @update:model-value="(value) => updateAttribute(selector.field_name, String(value || ''))">
          <SelectTrigger>
            <SelectValue :placeholder="selector.placeholder || '请选择'" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem v-for="option in selector.options" :key="option.value" :value="String(option.value)">
              {{ option.name }}
            </SelectItem>
          </SelectContent>
        </Select>

        <!-- 多选下拉框 -->
        <Popover v-else>
          <PopoverTrigger as-child>
            <Button variant="outline" class="justify-between py-2 w-full h-auto font-normal min-h-9">
              <div class="flex overflow-hidden flex-1 items-center">
                <div v-if="!getSelectedValues(selector.field_name).length" class="truncate text-muted-foreground">
                  {{ selector.placeholder || '请选择' }}
                </div>
                <div v-else class="flex overflow-hidden gap-1 items-center">
                  <!-- 显示选中的标签 -->
                  <div v-for="selectedName in getSelectedNames(selector, 2)" :key="selectedName"
                    class="inline-flex gap-1 items-center px-1.5 py-0.5 text-xs rounded bg-secondary text-secondary-foreground shrink-0">
                    <span class="truncate max-w-16" :title="selectedName">{{ selectedName }}</span>
                    <button @click.stop="removeSelectedByName(selector, selectedName)" class="hover:text-destructive">
                      <X class="w-3 h-3" />
                    </button>
                  </div>
                  <!-- 显示剩余数量 -->
                  <div v-if="getSelectedValues(selector.field_name).length > 2"
                    class="text-xs text-muted-foreground shrink-0">
                    +{{ getSelectedValues(selector.field_name).length - 2 }}
                  </div>
                </div>
              </div>
              <ChevronDown class="ml-2 w-4 h-4 opacity-50 shrink-0" />
            </Button>
          </PopoverTrigger>
          <PopoverContent class="w-[--radix-popover-trigger-width] p-0" align="start">
            <!-- 搜索框 -->
            <div class="p-2 border-b">
              <Input v-model="searchQuery[selector.field_name]" :placeholder="`搜索${selector.label}...`" class="h-8" />
            </div>

            <!-- 选项列表 -->
            <div class="overflow-y-auto max-h-60">
              <div v-if="!getFilteredOptions(selector).length" class="px-3 py-2 text-sm text-muted-foreground">
                未找到相关选项
              </div>
              <div v-for="option in getFilteredOptions(selector)" :key="option.value"
                class="flex justify-between items-center px-3 py-2 text-sm cursor-pointer hover:bg-accent"
                @click="toggleOption(selector.field_name, String(option.value))">
                <span>{{ option.name }}</span>
                <Check v-if="isOptionSelected(selector.field_name, String(option.value))"
                  class="w-4 h-4 text-primary" />
              </div>
            </div>

            <!-- 底部状态栏 -->
            <div v-if="getSelectedValues(selector.field_name).length > 0"
              class="flex justify-between items-center px-3 py-2 text-xs border-t bg-muted/30">
              <span class="text-muted-foreground">已选择 {{ getSelectedValues(selector.field_name).length }} 项</span>
              <button @click="clearAllSelections(selector.field_name)" class="text-destructive hover:underline">
                清空
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ChevronDown, X, Check } from 'lucide-vue-next'

// 属性选择器接口
export interface AttributeSelector {
  field_name: string
  label: string
  type: 'select' | 'multiselect' // 保留原有字段用于兼容性
  upload_type: 'select' | 'multiselect' // 用于上传功能
  placeholder?: string
  options: Array<{
    name: string
    value: string | number
  }>
  required?: boolean
  is_form: string  // 是否在表单中显示 "Y" 或 "N"
}

export type AttributeSelectorChangeEvent = {
  fieldName: string // 触发变更的字段名
  allValues: Record<string, string | number | (string | number)[]> // 所有字段的完整值
}

// Props
const props = defineProps<{
  attributeSelectors: AttributeSelector[]
  modelValue: Record<string, string | number | (string | number)[]>
}>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, string | number | (string | number)[]>]
  'change': [payload: AttributeSelectorChangeEvent]
}>()

// ========== 核心状态管理 ==========
const searchQuery = ref<Record<string, string>>({})

// 过滤出可见的属性选择器（is_form 为 "Y" 的），并过滤掉"未选择"选项
const visibleAttributeSelectors = computed(() => {
  return props.attributeSelectors.filter(selector => selector.is_form === 'Y').map(selector => ({
    ...selector,
    options: selector.options.filter(option => option.name !== '未选择')
  }))
})

// ========== 工具函数 ==========
const createInitialValue = (type: 'select' | 'multiselect'): string | (string | number)[] =>
  type === 'multiselect' ? [] : ''

const isValidSelector = (fieldName: string): boolean => {
  return visibleAttributeSelectors.value.some(s => s.field_name === fieldName)
}

const findOption = (selector: AttributeSelector, value: string) => {
  return selector.options.find(opt => String(opt.value) === value || opt.name === value)
}

// ========== 核心更新逻辑 ==========
const emitUpdate = (fieldName: string, value: string | number | (string | number)[], clearSearch = false) => {
  if (!isValidSelector(fieldName)) return

  const newModelValue = { ...props.modelValue, [fieldName]: value }

  if (clearSearch && searchQuery.value[fieldName] !== undefined) {
    searchQuery.value[fieldName] = ''
  }

  emit('update:modelValue', newModelValue)
  emit('change', { fieldName, allValues: newModelValue })
}

// ========== 响应式数据同步 ==========
watch(() => props.attributeSelectors, () => {
  const visibleFieldNames = new Set(visibleAttributeSelectors.value.map(s => s.field_name))

  // 智能清理和初始化数据
  const cleanedModelValue = Object.fromEntries(
    Object.entries(props.modelValue).filter(([fieldName]) => visibleFieldNames.has(fieldName))
  )

  // 批量初始化新字段
  visibleAttributeSelectors.value.forEach(selector => {
    if (!(selector.field_name in cleanedModelValue)) {
      cleanedModelValue[selector.field_name] = createInitialValue(selector.upload_type)
    }
  })

  // 同步搜索状态
  searchQuery.value = Object.fromEntries(
    visibleAttributeSelectors.value.map(selector => [
      selector.field_name,
      searchQuery.value[selector.field_name] || ''
    ])
  )

  emit('update:modelValue', cleanedModelValue)
}, { immediate: true })

// ========== 数据访问器 ==========
const dataAccessors = {
  // 获取选中的值
  getSelectedValues: (fieldName: string): (string | number)[] => {
    const value = props.modelValue[fieldName]
    return Array.isArray(value) ? value : []
  },

  // 检查选项是否被选中
  isOptionSelected: (fieldName: string, optionValue: string): boolean => {
    return dataAccessors.getSelectedValues(fieldName).some(v => String(v) === optionValue)
  },

  // 获取已选择选项的名称
  getSelectedNames: (selector: AttributeSelector, limit?: number): string[] => {
    const selectedValues = dataAccessors.getSelectedValues(selector.field_name)
    const names = selectedValues
      .map(value => findOption(selector, String(value))?.name || String(value))
      .filter(Boolean)

    return limit ? names.slice(0, limit) : names
  }
}

// ========== 选项过滤和搜索 ==========
const optionFilters = {
  // 获取过滤后的选项
  getFilteredOptions: (selector: AttributeSelector) => {
    const query = (searchQuery.value[selector.field_name] || '').toLowerCase().trim()

    return query
      ? selector.options.filter(option =>
        option.name.toLowerCase().includes(query) ||
        String(option.value).toLowerCase().includes(query)
      )
      : selector.options
  }
}

// ========== 业务操作 ==========
const operations = {
  // 更新属性
  updateAttribute: (fieldName: string, value: string) => {
    emitUpdate(fieldName, value)
  },

  // 切换选项
  toggleOption: (fieldName: string, optionValue: string) => {
    const currentValues = dataAccessors.getSelectedValues(fieldName)
    const newValues = currentValues.includes(optionValue)
      ? currentValues.filter(v => String(v) !== optionValue)
      : [...currentValues, optionValue]

    emitUpdate(fieldName, newValues)
  },

  // 根据名称移除选项
  removeSelectedByName: (selector: AttributeSelector, optionName: string) => {
    const option = findOption(selector, optionName)
    if (option) {
      operations.toggleOption(selector.field_name, String(option.value))
    }
  },

  // 清空所有选择
  clearAllSelections: (fieldName: string) => {
    emitUpdate(fieldName, [], true)
  }
}

// ========== 验证逻辑 ==========
const validation = {
  // 验证必填字段
  validateRequiredFields: (): boolean => {
    return visibleAttributeSelectors.value
      .filter(s => s.required)
      .every(selector => {
        const value = props.modelValue[selector.field_name]
        return Array.isArray(value) ? value.length > 0 : Boolean(value?.toString().trim())
      })
  }
}

// ========== 对外接口（保持向后兼容） ==========
const { getSelectedValues, isOptionSelected, getSelectedNames } = dataAccessors
const { getFilteredOptions } = optionFilters
const { updateAttribute, toggleOption, removeSelectedByName, clearAllSelections } = operations
const { validateRequiredFields } = validation

// 暴露验证方法给父组件
defineExpose({ validateRequiredFields })
</script>

<style scoped>
.attribute-selectors {
  border-bottom: 1px solid hsl(var(--border));
  padding-bottom: 1.5rem;
}

.attribute-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
}

/* 响应式设计 */
@media (max-width: 640px) {
  .attribute-grid {
    grid-template-columns: 1fr;
  }
}
</style>