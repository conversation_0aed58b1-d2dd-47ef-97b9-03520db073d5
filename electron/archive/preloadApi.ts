import { ip<PERSON><PERSON><PERSON><PERSON> } from "electron";
import type { ArchiveOptions, BatchPackingOptions, ArchiveApiResponse } from "./types";

/**
 * 压缩 Preload API 接口
 */
export interface ArchivePreloadApi {
  // 智能打包分析
  analyzeSmartPacking: (filePaths: string[], options?: BatchPackingOptions) => Promise<ArchiveApiResponse>;
  
  // 任务管理
  createTask: (sourcePaths: string[], options?: ArchiveOptions) => Promise<ArchiveApiResponse>;
  startTask: (taskId: string) => Promise<ArchiveApiResponse>;
  cancelTask: (taskId: string) => Promise<ArchiveApiResponse>;
  getTask: (taskId: string) => Promise<ArchiveApiResponse>;
  getAllTasks: () => Promise<ArchiveApiResponse>;
  cleanupCompleted: () => Promise<ArchiveApiResponse>;
  
  // 用户交互
  showConfirmationDialog: (analysis: any) => Promise<ArchiveApiResponse>;
  
  // 事件监听器
  onTaskCreated: (callback: (taskId: string, task: any) => void) => void;
  onTaskProgress: (callback: (taskId: string, progress: number, currentFile?: string) => void) => void;
  onTaskCompleted: (callback: (taskId: string, result: any) => void) => void;
  onTaskError: (callback: (taskId: string, error: string) => void) => void;
  onTaskCancelled: (callback: (taskId: string) => void) => void;
  
  // 清理监听器
  removeAllListeners: () => void;
}

/**
 * 压缩事件监听器接口
 */
export interface ArchiveEventListeners {
  onTaskCreated: (callback: (taskId: string, task: any) => void) => void;
  onTaskProgress: (callback: (taskId: string, progress: number, currentFile?: string) => void) => void;
  onTaskCompleted: (callback: (taskId: string, result: any) => void) => void;
  onTaskError: (callback: (taskId: string, error: string) => void) => void;
  onTaskCancelled: (callback: (taskId: string) => void) => void;
  removeAllListeners: () => void;
}

/**
 * 创建压缩 API
 */
export function createArchiveApi(): ArchivePreloadApi {
  return {
    // 智能打包分析
    analyzeSmartPacking: (filePaths: string[], options?: BatchPackingOptions) =>
      ipcRenderer.invoke("archive-analyze-smart-packing", filePaths, options),

    // 任务管理
    createTask: (sourcePaths: string[], options?: ArchiveOptions) =>
      ipcRenderer.invoke("archive-create-task", sourcePaths, options),
    
    startTask: (taskId: string) =>
      ipcRenderer.invoke("archive-start-task", taskId),
    
    cancelTask: (taskId: string) =>
      ipcRenderer.invoke("archive-cancel-task", taskId),
    
    getTask: (taskId: string) =>
      ipcRenderer.invoke("archive-get-task", taskId),
    
    getAllTasks: () =>
      ipcRenderer.invoke("archive-get-all-tasks"),
    
    cleanupCompleted: () =>
      ipcRenderer.invoke("archive-cleanup-completed"),

    // 用户交互
    showConfirmationDialog: (analysis: any) =>
      ipcRenderer.invoke("archive-show-confirmation-dialog", analysis),

    // 事件监听器
    onTaskCreated: (callback) => {
      ipcRenderer.on("archive-task-created", (_event, taskId, task) => callback(taskId, task));
    },

    onTaskProgress: (callback) => {
      ipcRenderer.on("archive-task-progress", (_event, taskId, progress, currentFile) => 
        callback(taskId, progress, currentFile)
      );
    },

    onTaskCompleted: (callback) => {
      ipcRenderer.on("archive-task-completed", (_event, taskId, result) => callback(taskId, result));
    },

    onTaskError: (callback) => {
      ipcRenderer.on("archive-task-error", (_event, taskId, error) => callback(taskId, error));
    },

    onTaskCancelled: (callback) => {
      ipcRenderer.on("archive-task-cancelled", (_event, taskId) => callback(taskId));
    },

    removeAllListeners: () => {
      const events = [
        "archive-task-created",
        "archive-task-progress", 
        "archive-task-completed",
        "archive-task-error",
        "archive-task-cancelled"
      ];
      events.forEach(event => ipcRenderer.removeAllListeners(event));
    }
  };
}

/**
 * 创建压缩事件监听器
 */
export function createArchiveEventListeners(): ArchiveEventListeners {
  return {
    onTaskCreated: (callback) => {
      ipcRenderer.on("archive-task-created", (_event, taskId, task) => callback(taskId, task));
    },

    onTaskProgress: (callback) => {
      ipcRenderer.on("archive-task-progress", (_event, taskId, progress, currentFile) => 
        callback(taskId, progress, currentFile)
      );
    },

    onTaskCompleted: (callback) => {
      ipcRenderer.on("archive-task-completed", (_event, taskId, result) => callback(taskId, result));
    },

    onTaskError: (callback) => {
      ipcRenderer.on("archive-task-error", (_event, taskId, error) => callback(taskId, error));
    },

    onTaskCancelled: (callback) => {
      ipcRenderer.on("archive-task-cancelled", (_event, taskId) => callback(taskId));
    },

    removeAllListeners: () => {
      const events = [
        "archive-task-created",
        "archive-task-progress", 
        "archive-task-completed",
        "archive-task-error",
        "archive-task-cancelled"
      ];
      events.forEach(event => ipcRenderer.removeAllListeners(event));
    }
  };
}

/**
 * 创建简化的压缩 Preload API（整合了 API 调用和事件监听器）
 */
export function createSimplifiedArchivePreloadApi(): ArchivePreloadApi {
  const api = createArchiveApi();
  const eventListeners = createArchiveEventListeners();

  return {
    ...api,
    ...eventListeners,
  };
}
