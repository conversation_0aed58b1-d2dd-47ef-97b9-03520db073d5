<template>
  <Transition name="detail-panel" enter-active-class="detail-panel-enter-active"
    leave-active-class="detail-panel-leave-active" enter-from-class="detail-panel-enter-from"
    leave-to-class="detail-panel-leave-to">
    <div v-if="selectedItem" class="flex flex-col h-full border bg-card/50">
      <!-- 头部 -->
      <div class="flex justify-between items-center p-4 border-b">
        <h3 class="text-lg font-semibold">详细信息</h3>
        <Button variant="ghost" size="icon" @click="$emit('close')">
          <X class="w-4 h-4" />
        </Button>
      </div>

      <!-- 内容区域 -->
      <div class="overflow-y-auto flex-1">
        <!-- 文件/文件夹图标和名称 -->
        <div class="p-4 text-center border-b">
          <div class="flex justify-center mb-3">
            <!-- 文件夹图标 -->
            <div v-if="selectedItem.type === 'folder'"
              class="flex justify-center items-center w-16 h-16 rounded-lg bg-muted">
              <Folder class="w-8 h-8 text-blue-500" />
            </div>
            <!-- 文件图标 -->
            <div v-else class="flex justify-center items-center rounded-lg bg-muted">
              <!-- 有预览图时显示预览图 -->
              <img v-if="selectedItem.thumbnailMedium" class="object-contain h-[150px]"
                :src="selectedItem.thumbnailMedium" :alt="selectedItem.name" />
              <!-- 没有预览图时显示文件类型图标 -->
              <div v-else class="flex justify-center items-center w-32 h-32">
                <component :is="getFileTypeIcon(selectedItem.type)" class="w-16 h-16"
                  :class="getFileTypeColor(selectedItem.type)" />
              </div>
            </div>
          </div>
          <h4 class="text-base font-medium break-all">{{ selectedItem.name }}</h4>
        </div>

        <!-- 自定义属性 -->
        <EditableCustomProperties v-if="customProperties" :properties="customProperties"
          :is-editing="isEditing || false" :editing-properties="editingProperties || {}" :is-saving="isSaving || false"
          :editable-properties="editableProperties || []" :start-editing="startEditing || (() => { })"
          :cancel-editing="cancelEditing || (() => { })" :save-editing="saveEditing || (async () => { })"
          :update-editing-property="updateEditingProperty || (() => { })"
          :format-property-value="formatPropertyValue || ((value: any) => String(value))"
          :get-value-class="getValueClass || (() => '')" />

        <!-- 基础信息 -->
        <BasicInfo :item="selectedItem" />

        <!-- 文件夹属性 -->
        <FolderProperties v-if="selectedItem.type === 'folder'" :item="selectedItem as FolderItemType" />

        <!-- 文件属性 -->
        <FileProperties v-else :item="selectedItem as FileItemType" />
      </div>
    </div>
  </Transition>

  <!-- 空状态 (不参与动画，始终显示) -->
  <div v-if="!selectedItem" class="flex flex-col justify-center items-center h-full text-center border-l bg-muted/20">
    <Info class="mb-4 w-12 h-12 text-muted-foreground/50" />
    <p class="text-base font-medium text-muted-foreground">选择文件查看详情</p>
    <p class="text-sm text-muted-foreground/70">点击任意文件或文件夹查看详细信息</p>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
  X,
  Folder,
  Info,
} from 'lucide-vue-next'
import BasicInfo from './BasicInfo.vue'
import FolderProperties from './FolderProperties.vue'
import FileProperties from './FileProperties.vue'
import EditableCustomProperties from './EditableCustomProperties.vue'
import { useFolderConfig } from '@/composables/useFolderConfig'
import type { ItemType, FileItemType, FolderItemType } from '@/types/files'
import type { EditableProperty } from './useCustomProperties'

// 获取文件类型图标配置
const { getFileTypeIcon, getFileTypeColor } = useFolderConfig()

// Props
defineProps<{
  selectedItem?: ItemType | null
  customProperties?: Record<string, any>
  // 编辑相关属性
  isEditing?: boolean
  editingProperties?: Record<string, any>
  isSaving?: boolean
  editableProperties?: EditableProperty[]
  startEditing?: () => void
  cancelEditing?: () => void
  saveEditing?: () => Promise<void>
  updateEditingProperty?: (fieldName: string, value: any) => void
  formatPropertyValue?: (value: any) => string
  getValueClass?: (value: any) => string
}>()

// Emits
defineEmits<{
  close: []
}>()
</script>

<style scoped>
/* 确保滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* 详细面板动画 */
.detail-panel-enter-active,
.detail-panel-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-panel-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.detail-panel-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

/* 优化动画性能 */
.detail-panel-enter-active,
.detail-panel-leave-active {
  will-change: transform, opacity;
}
</style>