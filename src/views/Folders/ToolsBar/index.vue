<template>
  <div class="flex gap-4 justify-between items-center p-4 border-b bg-background">
    <!-- 左侧：搜索框 -->
    <div class="flex flex-1 gap-2 items-center max-w-md">
      <div class="relative flex-1">
        <Search class="absolute left-3 top-1/2 w-4 h-4 -translate-y-1/2 text-muted-foreground" />
        <Input v-model="searchQuery" placeholder="搜索文件..." class="pr-9 pl-9" />
        <Button v-if="searchQuery" variant="ghost" size="icon" class="absolute right-1 top-1/2 w-7 h-7 -translate-y-1/2"
          @click="clearSearch">
          <X class="w-3 h-3" />
        </Button>
      </div>
    </div>

    <!-- 右侧：操作区域 -->
    <div class="flex gap-3 items-center">
      <!-- 文件计数和刷新 -->
      <div class="flex gap-2 items-center">
        <span class="text-sm text-muted-foreground">
          共 {{ fileCount }} 项
        </span>
        <Button variant="ghost" size="icon" class="w-8 h-8" @click="handleRefresh" title="刷新">
          <RotateCcw class="w-4 h-4" />
        </Button>
      </div>

      <!-- 分隔线 -->
      <Separator orientation="vertical" class="h-6" />

      <!-- 视图切换 -->
      <div class="flex items-center rounded-md border">
        <Button variant="ghost" size="icon" class="w-8 h-8 rounded-r-none"
          :class="viewMode === 'grid' ? 'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground' : ''"
          @click="setViewMode('grid')" title="网格视图">
          <Grid3X3 class="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="icon" class="w-8 h-8 rounded-l-none"
          :class="viewMode === 'list' ? 'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground' : ''"
          @click="setViewMode('list')" title="列表视图">
          <List class="w-4 h-4" />
        </Button>
      </div>

      <!-- 分隔线 -->
      <Separator orientation="vertical" class="h-6" />

      <!-- 操作按钮 -->
      <div class="flex gap-2 items-center">
        <!-- <Button variant="outline" size="sm" @click="handleCreateFolder">
          <FolderPlus class="mr-2 w-4 h-4" />
          新建文件夹
        </Button> -->
        <Button size="sm" @click="handleUpload">
          <Upload class="mr-2 w-4 h-4" />
          上传文件
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import {
  Search,
  X,
  RotateCcw,
  Grid3X3,
  List,
  // FolderPlus,
  Upload
} from 'lucide-vue-next'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { watch } from 'vue'
import { useViewMode } from '@/composables/useViewMode'
import { useSearch } from '@/composables/useSearch'

// Props
defineProps<{
  fileCount: number
}>()

// Emits
const emit = defineEmits<{
  search: [query: string]
  refresh: []
  createFolder: []
  upload: []
}>()

// 使用视图模式 composable
const { viewMode, setViewMode: setInternalViewMode } = useViewMode()

// 使用搜索功能 composable
const { searchQuery, handleSearchInput, clearSearch } = useSearch({
  debounceDelay: 300,
  onSearch: (query: string) => {
    emit('search', query)
  }
})

// 监听搜索查询变化，处理中文输入法问题
watch(searchQuery, (newQuery) => {
  handleSearchInput(newQuery)
}, { immediate: false })

// 设置视图模式
const setViewMode = (mode: 'grid' | 'list') => {
  setInternalViewMode(mode)
}

// 搜索功能现在由 useSearch composable 处理

// 处理刷新
const handleRefresh = () => {
  emit('refresh')
}

// 处理新建文件夹
// const handleCreateFolder = () => {
//   emit('createFolder')
// }

// 处理上传文件
const handleUpload = () => {
  emit('upload')
}

// 视图模式现在通过全局状态自动同步
// 搜索功能的定时器清理现在由 useSearch composable 自动处理
</script>
