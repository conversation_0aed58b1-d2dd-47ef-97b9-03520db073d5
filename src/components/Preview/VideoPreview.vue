<template>
  <div class="relative w-full h-[90vh] flex items-center justify-center bg-black">
    <!-- 加载状态 -->
    <div v-if="isLoading"
      class="flex absolute inset-0 z-10 justify-center items-center backdrop-blur-sm bg-background/80">
      <div class="flex flex-col gap-2 items-center">
        <div class="w-8 h-8 rounded-full border-b-2 animate-spin border-primary"></div>
        <p class="text-sm text-muted-foreground">加载中...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex flex-col gap-4 items-center p-8 text-center">
      <div class="flex justify-center items-center w-16 h-16 rounded-full bg-destructive/10">
        <svg class="w-8 h-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div>
        <h3 class="mb-1 font-medium text-foreground">视频加载失败</h3>
        <p class="text-sm text-muted-foreground">{{ error }}</p>
      </div>
      <button @click="retryLoad"
        class="px-4 py-2 rounded-md transition-colors bg-primary text-primary-foreground hover:bg-primary/90">
        重试
      </button>
    </div>

    <!-- 视频预览 -->
    <div v-else class="flex relative justify-center items-center w-full h-full">
      <video ref="videoRef" :src="videoUrl" class="max-w-full max-h-full" controls preload="metadata"
        @loadstart="handleLoadStart" @loadeddata="handleLoadedData" @error="handleVideoError" @play="handlePlay"
        @pause="handlePause" @timeupdate="handleTimeUpdate" @volumechange="handleVolumeChange">
        <p class="text-muted-foreground">您的浏览器不支持视频播放。</p>
      </video>

      <!-- 自定义控制栏（可选，当前使用原生控制栏） -->
      <div v-if="showCustomControls"
        class="flex absolute bottom-4 left-1/2 gap-3 items-center px-4 py-2 rounded-lg shadow-lg backdrop-blur-sm transform -translate-x-1/2 bg-background/90">
        <!-- 播放/暂停按钮 -->
        <button @click="togglePlay" class="p-1 rounded hover:bg-accent">
          <svg v-if="!isPlaying" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z" />
          </svg>
          <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
          </svg>
        </button>

        <!-- 进度条 -->
        <div class="flex items-center gap-2 min-w-[200px]">
          <span class="text-xs text-muted-foreground min-w-[3rem]">
            {{ formatTime(currentTime) }}
          </span>
          <div class="overflow-hidden flex-1 h-1 rounded-full bg-accent">
            <div class="h-full transition-all duration-100 bg-primary" :style="{ width: `${progress}%` }"></div>
          </div>
          <span class="text-xs text-muted-foreground min-w-[3rem]">
            {{ formatTime(duration) }}
          </span>
        </div>

        <!-- 音量控制 -->
        <div class="flex gap-1 items-center">
          <button @click="toggleMute" class="p-1 rounded hover:bg-accent">
            <svg v-if="!isMuted && volume > 0.5" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z" />
            </svg>
            <svg v-else-if="!isMuted && volume > 0" class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z" />
            </svg>
            <svg v-else class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
              <path
                d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z" />
            </svg>
          </button>
          <div class="overflow-hidden w-16 h-1 rounded-full bg-accent">
            <div class="h-full bg-primary" :style="{ width: `${volume * 100}%` }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface Props {
  videoUrl: string
  fileName?: string
  isLoading?: boolean
  error?: string | null
  showCustomControls?: boolean
}

interface Emits {
  (e: 'retry'): void
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  error: null,
  showCustomControls: false
})

const emit = defineEmits<Emits>()

// 视频元素引用
const videoRef = ref<HTMLVideoElement>()

// 播放状态
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(false)

// 计算进度百分比
const progress = computed(() => {
  if (duration.value === 0) return 0
  return (currentTime.value / duration.value) * 100
})

// 监听URL变化，重置状态
watch(() => props.videoUrl, () => {
  resetVideoState()
}, { immediate: true })

/**
 * 重置视频状态
 */
function resetVideoState() {
  isPlaying.value = false
  currentTime.value = 0
  duration.value = 0
}

/**
 * 处理视频开始加载
 */
function handleLoadStart() {
  resetVideoState()
}

/**
 * 处理视频数据加载完成
 */
function handleLoadedData() {
  if (videoRef.value) {
    duration.value = videoRef.value.duration || 0
    volume.value = videoRef.value.volume
    isMuted.value = videoRef.value.muted
  }
}

/**
 * 处理视频加载错误
 */
function handleVideoError() {
  // 错误处理由父组件通过props传入
}

/**
 * 重试加载
 */
function retryLoad() {
  emit('retry')
}

/**
 * 处理播放事件
 */
function handlePlay() {
  isPlaying.value = true
}

/**
 * 处理暂停事件
 */
function handlePause() {
  isPlaying.value = false
}

/**
 * 处理时间更新
 */
function handleTimeUpdate() {
  if (videoRef.value) {
    currentTime.value = videoRef.value.currentTime
  }
}

/**
 * 处理音量变化
 */
function handleVolumeChange() {
  if (videoRef.value) {
    volume.value = videoRef.value.volume
    isMuted.value = videoRef.value.muted
  }
}

/**
 * 切换播放/暂停
 */
function togglePlay() {
  if (!videoRef.value) return

  if (isPlaying.value) {
    videoRef.value.pause()
  } else {
    videoRef.value.play()
  }
}

/**
 * 切换静音
 */
function toggleMute() {
  if (!videoRef.value) return

  videoRef.value.muted = !videoRef.value.muted
}

/**
 * 格式化时间显示
 */
function formatTime(seconds: number): string {
  if (!isFinite(seconds)) return '00:00'

  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)

  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
</script>
