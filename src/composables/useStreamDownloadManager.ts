import { ref, computed } from "vue";
import { toast } from "vue-sonner";
import type { ItemType, FileItemType, FolderItemType } from "@/types/files";
import filesApi from "@/api/services/files";

/**
 * 解析文件大小字符串为字节数
 * @param sizeStr 文件大小字符串，如 "33.23 MB"
 * @returns 字节数
 */
function parseFileSizeString(sizeStr: string): number | undefined {
  const units: Record<string, number> = {
    B: 1,
    KB: 1024,
    MB: 1024 * 1024,
    GB: 1024 * 1024 * 1024,
    TB: 1024 * 1024 * 1024 * 1024,
  };

  const match = sizeStr.trim().match(/^([\d.]+)\s*([A-Z]*B?)$/i);
  if (!match) {
    console.warn(`无法解析文件大小字符串: "${sizeStr}"`);
    return undefined;
  }

  const size = parseFloat(match[1]);
  const unit = match[2].toUpperCase() as keyof typeof units;
  const multiplier = units[unit] || 1;

  const result = Math.round(size * multiplier);
  return result;
}

/** 下载任务状态类型 */
export type DownloadStatus = "pending" | "downloading" | "paused" | "completed" | "error" | "cancelled" | "extracting" | "extract-completed" | "extract-error";

/** 解压缩任务状态类型 */
export type ExtractionStatus = "pending" | "extracting" | "paused" | "completed" | "error" | "cancelled";

/** 下载任务接口 */
export interface DownloadTask {
  id: string;
  fileName: string;
  fileSize?: number;
  progress: number;
  status: DownloadStatus;
  bytesDownloaded: number;
  downloadSpeed: number;
  remainingTime: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
  streamTaskId?: string; // Electron端任务ID
  isSubTask?: boolean; // 是否为批量下载的子任务
  batchId?: string; // 所属批量任务ID

  // 解压缩相关字段
  needsExtraction?: boolean; // 是否需要解压缩（7z文件）
  extractionTaskId?: string; // 关联的解压缩任务ID
  extractionStatus?: ExtractionStatus; // 解压缩状态
  extractionProgress?: number; // 解压缩进度 (0-100)
  extractionError?: string; // 解压缩错误信息
  extractPath?: string; // 解压目标路径
  deleteAfterExtraction?: boolean; // 解压完成后是否删除原文件

  // 解压缩进度相关字段
  extractedFiles?: number; // 已解压文件数量
  totalFiles?: number; // 总文件数量
  speed?: number; // 解压缩速度
  currentFile?: string; // 当前正在解压的文件
}

/** 批量下载任务接口 */
export interface BatchDownloadTask {
  id: string;
  type: "batch";
  batchName: string;
  folderPath?: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  pausedFiles: number;
  totalSize: number;
  downloadedSize: number;
  progress: number;
  status: DownloadStatus;
  startTime: Date;
  endTime?: Date;
  duration: number;
  error?: string;
  subTasks: string[]; // 子任务ID列表
  expanded?: boolean; // 展开状态
  metadata?: Record<string, any>;
  avgDownloadSpeed: number;
  estimatedRemainingTime: number;
}

/** 下载配置接口 */
export interface DownloadConfig {
  savePath?: string;
  category_id: number | string;
}

/**
 * 生成唯一ID
 * @param prefix - ID前缀，默认为"download"
 * @returns 生成的唯一ID字符串
 */
const generateUniqueId = (prefix: string = "download"): string => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

// 全局状态
const tasks = ref<Map<string, DownloadTask>>(new Map());
const batchTasks = ref<Map<string, BatchDownloadTask>>(new Map());

/**
 * 流式下载管理器 Composable
 * 提供完整的下载任务管理功能，包括单文件下载、文件夹批量下载、任务控制等
 * @returns 下载管理器的状态和方法
 */
export function useStreamDownloadManager() {
  /** 检查是否在Electron环境 */
  const isElectron = computed(() => typeof window !== "undefined" && !!window.electronAPI);

  /** 检查文件是否为7z格式 */
  const is7zFile = (fileName: string): boolean => {
    return fileName.toLowerCase().endsWith(".7z");
  };

  /**
   * 获取Electron API
   * @returns Electron API对象
   * @throws 如果不在Electron环境中则抛出错误
   */
  const getElectronAPI = () => {
    if (!isElectron.value) {
      throw new Error("下载功能仅在Electron环境中可用");
    }
    return window.electronAPI!;
  };

  /**
   * 将Electron状态映射为下载状态
   * @param electronStatus - Electron端的状态字符串
   * @returns 映射后的下载状态
   */
  const mapElectronStatusToDownloadStatus = (electronStatus: string): DownloadStatus => {
    const statusMap: Record<string, DownloadStatus> = {
      pending: "pending",
      downloading: "downloading",
      paused: "paused",
      completed: "completed",
      error: "error",
      cancelled: "cancelled",
    };
    return statusMap[electronStatus] || "pending";
  };

  // 计算属性
  const allTasks = computed(() => Array.from(tasks.value.values()));
  const standaloneTasks = computed(() => allTasks.value.filter((task) => !task.isSubTask));
  const activeTasks = computed(() => standaloneTasks.value.filter((task) => ["pending", "downloading", "paused"].includes(task.status)));
  const completedTasks = computed(() => standaloneTasks.value.filter((task) => task.status === "completed"));
  const errorTasks = computed(() => standaloneTasks.value.filter((task) => task.status === "error"));

  const allBatchTasks = computed(() => Array.from(batchTasks.value.values()));
  const activeBatchTasks = computed(() => allBatchTasks.value.filter((batch) => ["pending", "downloading", "paused"].includes(batch.status)));

  /** 计算总体下载进度 */
  const totalProgress = computed(() => {
    const activeBatches = activeBatchTasks.value;
    const activeStandalone = standaloneTasks.value.filter((task) => ["pending", "downloading", "paused"].includes(task.status));

    const totalTasks = activeBatches.length + activeStandalone.length;
    if (totalTasks === 0) return 0;

    const totalProgress = activeBatches.reduce((sum, batch) => sum + batch.progress, 0) + activeStandalone.reduce((sum, task) => sum + task.progress, 0);

    return Math.round(totalProgress / totalTasks);
  });

  /**
   * 构建下载参数
   * @param fileId - 文件ID
   * @param categoryId - 分类ID
   * @returns 下载参数对象
   */
  const buildDownloadParams = (fileId: number | string, categoryId: number | string) => ({
    category_id: String(categoryId),
    file_id: String(fileId),
  });

  /**
   * 处理任务创建事件
   * 当Electron端创建新任务时，同步到前端状态
   * @param streamTaskId - Electron端任务ID
   * @param electronTask - Electron端任务数据
   */
  const handleTaskCreated = (streamTaskId: string, electronTask: any) => {
    console.log("下载任务创建:", streamTaskId);

    // 查找对应的本地任务
    const localTask = allTasks.value.find((task) => task.streamTaskId === streamTaskId);
    if (localTask) {
      // 更新本地任务信息
      const updatedTask: DownloadTask = {
        ...localTask,
        fileSize: electronTask.fileSize,
        bytesDownloaded: electronTask.bytesDownloaded || 0,
        downloadSpeed: electronTask.downloadSpeed || 0,
        remainingTime: electronTask.remainingTime || 0,
      };
      tasks.value.set(localTask.id, updatedTask);

      if (localTask.batchId) {
        updateBatchTaskProgress(localTask.batchId);
      }
    } else {
      // 检查是否已经有相同文件名的任务（避免重复）
      const existingTask = allTasks.value.find((task) => task.fileName === electronTask.fileName && task.status !== "completed" && task.status !== "cancelled");

      if (existingTask) {
        console.warn("发现重复任务，跳过创建:", electronTask.fileName);
        return;
      }

      // 这是一个从 Electron 端恢复的任务，创建对应的前端任务
      const newTask: DownloadTask = {
        id: generateUniqueId(),
        streamTaskId: streamTaskId,
        fileName: electronTask.fileName,
        fileSize: electronTask.fileSize,
        progress: electronTask.progress || 0,
        status: electronTask.status || "pending",
        bytesDownloaded: electronTask.bytesDownloaded || 0,
        downloadSpeed: electronTask.downloadSpeed || 0,
        remainingTime: electronTask.remainingTime || 0,
        startTime: electronTask.startTime ? new Date(electronTask.startTime) : new Date(),
        metadata: electronTask.metadata || {},
        isSubTask: electronTask.isSubTask,
        batchId: electronTask.batchId,
      };
      tasks.value.set(newTask.id, newTask);
    }
  };

  /**
   * 处理任务进度更新事件
   * @param streamTaskId - Electron端任务ID
   * @param progress - 下载进度百分比
   * @param bytesDownloaded - 已下载字节数
   * @param bytesTotal - 文件总字节数
   */
  const handleTaskProgress = (streamTaskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => {
    const task = allTasks.value.find((t) => t.streamTaskId === streamTaskId);
    if (task) {
      // 更新文件大小（如果之前没有或者现在有更准确的值）
      const finalFileSize = bytesTotal || task.fileSize || 0;

      // 使用 Electron 端计算的进度，确保进度不超过 100%
      const safeProgress = Math.min(100, Math.max(0, Math.round(progress)));

      const updatedTask: DownloadTask = {
        ...task,
        progress: safeProgress,
        bytesDownloaded,
        status: task.status === "pending" ? "downloading" : task.status,
        fileSize: finalFileSize,
      };
      tasks.value.set(task.id, updatedTask);

      // 强制触发响应式更新，确保进度任务能获取到最新的文件大小
      if (finalFileSize !== task.fileSize) {
        // 触发 tasks 的响应式更新
        tasks.value = new Map(tasks.value);
      }

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    } else {
      console.warn("未找到对应的下载任务:", streamTaskId);
    }
  };

  /**
   * 处理任务状态变化事件
   * @param streamTaskId - Electron端任务ID
   * @param status - 新的任务状态
   * @param error - 错误信息（可选）
   */
  const handleTaskStatusChanged = (streamTaskId: string, status: string, error?: string) => {
    console.log("下载状态变化:", streamTaskId, "新状态:", status);

    const task = allTasks.value.find((t) => t.streamTaskId === streamTaskId);
    if (task) {
      const mappedStatus = mapElectronStatusToDownloadStatus(status);
      const updatedTask: DownloadTask = {
        ...task,
        status: mappedStatus,
        error,
      };

      if (["completed", "error", "cancelled"].includes(status)) {
        updatedTask.endTime = new Date();
      }

      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    } else {
      console.warn("状态变化时未找到对应的下载任务:", streamTaskId);
    }
  };

  /**
   * 处理任务完成事件
   * @param streamTaskId - Electron端任务ID
   */
  const handleTaskCompleted = (streamTaskId: string) => {
    const task = allTasks.value.find((t) => t.streamTaskId === streamTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        status: "completed",
        progress: 100,
        endTime: new Date(),
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (!task.isSubTask) {
        toast.success(`文件 "${task.fileName}" 下载完成`);
      }
    }
  };

  /**
   * 处理任务错误事件
   * @param streamTaskId - Electron端任务ID
   * @param error - 错误信息
   */
  const handleTaskError = (streamTaskId: string, error: string) => {
    const task = allTasks.value.find((t) => t.streamTaskId === streamTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        status: "error",
        error,
        endTime: new Date(),
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (!task.isSubTask) {
        toast.error(`文件 "${task.fileName}" 下载失败: ${error}`);
      }
    }
  };

  /**
   * 处理解压缩进度事件
   * @param extractionTaskId - 解压缩任务ID
   * @param progress - 解压缩进度
   * @param extractedSize - 已解压大小
   * @param totalSize - 总大小
   */
  const handleExtractionProgress = (extractionTaskId: string, progress: number, extractedSize: number, totalSize?: number) => {
    const task = allTasks.value.find((t) => t.extractionTaskId === extractionTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        extractionProgress: progress,
        // 可以在这里使用 extractedSize 和 totalSize 来显示更详细的进度信息
        metadata: {
          ...task.metadata,
          extractedSize,
          totalSize,
        },
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    }
  };

  /**
   * 处理解压缩状态变化事件
   * @param extractionTaskId - 解压缩任务ID
   * @param status - 新的解压缩状态
   * @param error - 错误信息（可选）
   */
  const handleExtractionStatusChanged = (extractionTaskId: string, status: string, error?: string) => {
    const task = allTasks.value.find((t) => t.extractionTaskId === extractionTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        extractionStatus: status as ExtractionStatus,
        extractionError: error,
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    }
  };

  /**
   * 处理解压缩完成事件
   * @param extractionTaskId - 解压缩任务ID
   * @param extractPath - 解压路径
   */
  const handleExtractionCompleted = (extractionTaskId: string, extractPath: string) => {
    const task = allTasks.value.find((t) => t.extractionTaskId === extractionTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        extractionStatus: "completed",
        extractionProgress: 100,
        extractPath,
        status: "extract-completed",
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (!task.isSubTask) {
        toast.success(`文件 "${task.fileName}" 解压缩完成`);
      }
    }
  };

  /**
   * 处理解压缩错误事件
   * @param extractionTaskId - 解压缩任务ID
   * @param error - 错误信息
   */
  const handleExtractionError = (extractionTaskId: string, error: string) => {
    const task = allTasks.value.find((t) => t.extractionTaskId === extractionTaskId);
    if (task) {
      const updatedTask: DownloadTask = {
        ...task,
        extractionStatus: "error",
        extractionError: error,
        status: "extract-error",
      };
      tasks.value.set(task.id, updatedTask);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }

      if (!task.isSubTask) {
        toast.error(`文件 "${task.fileName}" 解压缩失败: ${error}`);
      }
    }
  };

  /**
   * 更新批量任务进度
   * 根据子任务状态计算批量任务的整体进度和状态
   * @param batchId - 批量任务ID
   */
  const updateBatchTaskProgress = (batchId: string) => {
    const batchTask = batchTasks.value.get(batchId);
    if (!batchTask) return;

    const subTasks = batchTask.subTasks.map((taskId) => tasks.value.get(taskId)).filter(Boolean) as DownloadTask[];

    const completedFiles = subTasks.filter((task) => task.status === "completed").length;
    const failedFiles = subTasks.filter((task) => task.status === "error").length;
    const pausedFiles = subTasks.filter((task) => task.status === "paused").length;

    const totalProgress = subTasks.reduce((sum, task) => sum + task.progress, 0);
    const avgProgress = subTasks.length > 0 ? Math.round(totalProgress / subTasks.length) : 0;

    const downloadedSize = subTasks.reduce((sum, task) => sum + task.bytesDownloaded, 0);
    const avgSpeed = subTasks.reduce((sum, task) => sum + task.downloadSpeed, 0);

    let status: DownloadStatus = batchTask.status;
    if (completedFiles === batchTask.totalFiles) {
      status = "completed";
    } else if (failedFiles > 0 && completedFiles + failedFiles === batchTask.totalFiles) {
      status = "error";
    } else if (subTasks.some((task) => task.status === "downloading")) {
      status = "downloading";
    } else if (pausedFiles > 0) {
      status = "paused";
    }

    const updatedBatch: BatchDownloadTask = {
      ...batchTask,
      completedFiles,
      failedFiles,
      pausedFiles,
      progress: avgProgress,
      downloadedSize,
      avgDownloadSpeed: avgSpeed,
      status,
    };

    if (status === "completed" && !batchTask.endTime) {
      updatedBatch.endTime = new Date();
      updatedBatch.duration = updatedBatch.endTime.getTime() - batchTask.startTime.getTime();
      toast.success(`批量下载完成: ${batchTask.batchName} (${completedFiles}/${batchTask.totalFiles} 个文件)`);
    }

    batchTasks.value.set(batchId, updatedBatch);
  };

  // 事件监听器设置
  const isListenersSetup = ref(false);

  /**
   * 设置下载事件监听器
   * 注册各种下载事件的处理函数，确保前端能接收到Electron端的状态更新
   */
  const setupEventListeners = () => {
    if (!isElectron.value) {
      console.warn("下载功能仅在Electron环境中可用");
      return;
    }

    if (isListenersSetup.value) {
      return;
    }

    try {
      const api = getElectronAPI();

      if (!api || !api.download) {
        throw new Error("Electron 下载 API 不可用");
      }

      console.log("设置下载事件监听器");

      api.download.onDownloadTaskCreated?.(handleTaskCreated);
      api.download.onDownloadTaskProgress?.(handleTaskProgress);
      api.download.onDownloadTaskStatusChanged?.(handleTaskStatusChanged);
      api.download.onDownloadTaskCompleted?.(handleTaskCompleted);
      api.download.onDownloadTaskError?.(handleTaskError);

      // 设置解压缩事件监听器
      if (api.extraction) {
        api.extraction.onExtractionTaskProgress?.(handleExtractionProgress);
        api.extraction.onExtractionTaskStatusChanged?.(handleExtractionStatusChanged);
        api.extraction.onExtractionTaskCompleted?.(handleExtractionCompleted);
        api.extraction.onExtractionTaskError?.(handleExtractionError);
      }

      isListenersSetup.value = true;
      console.log("下载事件监听器设置完成");
    } catch (error) {
      console.error("设置下载事件监听器失败:", error);
      throw error;
    }
  };

  /**
   * 恢复已存在的下载任务
   * 从Electron端获取现有任务并同步到前端状态，用于应用重启后的状态恢复
   */
  const restoreExistingTasks = async (retryCount = 0) => {
    try {
      const api = getElectronAPI();
      const result = await api.download.getAllTasks?.();

      if (result?.success && result.data) {
        const existingTasks = result.data as DownloadTask[];
        console.log(`从 Electron 端获取到 ${existingTasks.length} 个任务`);

        existingTasks.forEach((electronTask) => {
          // 检查是否已经有相同的任务（通过 streamTaskId 或文件名）
          const existingLocalTask = allTasks.value.find(
            (localTask) => localTask.streamTaskId === electronTask.id || (localTask.fileName === electronTask.fileName && localTask.status !== "completed" && localTask.status !== "cancelled")
          );

          if (existingLocalTask) return;

          // 创建前端任务
          const frontendTask: DownloadTask = {
            id: generateUniqueId(),
            streamTaskId: electronTask.id, // 使用 Electron 端的任务 ID 作为 streamTaskId
            fileName: electronTask.fileName,
            fileSize: electronTask.fileSize,
            progress: electronTask.progress || 0,
            status: electronTask.status || "pending",
            bytesDownloaded: electronTask.bytesDownloaded || 0,
            downloadSpeed: electronTask.downloadSpeed || 0,
            remainingTime: electronTask.remainingTime || 0,
            startTime: electronTask.startTime ? new Date(electronTask.startTime) : new Date(),
            metadata: electronTask.metadata || {},
          };

          tasks.value.set(frontendTask.id, frontendTask);
        });
      }
    } catch (error) {
      console.error("恢复已存在任务失败:", error);

      // 如果是 IPC 处理器未注册的错误，并且重试次数少于3次，则重试
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage?.includes("No handler registered") && retryCount < 3) {
        console.log(`IPC 处理器未就绪，${1000 * (retryCount + 1)}ms 后重试 (${retryCount + 1}/3)`);
        setTimeout(() => {
          restoreExistingTasks(retryCount + 1);
        }, 1000 * (retryCount + 1)); // 递增延迟：1s, 2s, 3s
      }
    }
  };

  // 在 composable 初始化时就设置事件监听器并恢复任务
  if (isElectron.value) {
    try {
      setupEventListeners();
      // 延迟更长时间确保 Electron 主进程模块完全初始化后再恢复任务
      setTimeout(async () => {
        await restoreExistingTasks();
        // 恢复任务后清理可能的重复任务
        cleanupDuplicateTasks();
      }, 2000); // 增加延迟时间到2秒，确保所有 Electron 模块初始化完成
    } catch (error) {
      console.error("初始化下载事件监听器失败:", error);
    }
  }

  /**
   * 下载单个文件
   * @param fileItem - 要下载的文件项
   * @param config - 下载配置
   * @returns 返回任务ID
   */
  const downloadSingleFile = async (fileItem: FileItemType, config: DownloadConfig): Promise<string> => {
    if (!isElectron.value) {
      throw new Error("下载功能仅在Electron环境中可用");
    }

    const taskId = generateUniqueId("download");
    const downloadParams = buildDownloadParams(fileItem.id, config.category_id);

    // 创建本地任务记录
    // 优先使用原始数值，避免字符串解析的精度损失
    let fileSize: number | undefined;
    if ("rawSize" in fileItem && typeof fileItem.rawSize === "number") {
      // 优先使用原始数值（最准确）
      fileSize = fileItem.rawSize;
    } else if (typeof fileItem.size === "number") {
      // 如果size本身就是数值
      fileSize = fileItem.size;
    } else if (typeof fileItem.size === "string" && fileItem.size) {
      // 最后才解析字符串格式（如 "33.23 MB"）
      fileSize = parseFileSizeString(fileItem.size);
    }

    console.log(`📊 文件大小解析: 原始值="${fileItem.size}", rawSize=${fileItem.rawSize}, 最终结果=${fileSize} bytes`);

    const task: DownloadTask = {
      id: taskId,
      fileName: fileItem.name,
      fileSize: fileSize,
      progress: 0,
      status: "pending",
      bytesDownloaded: 0,
      downloadSpeed: 0,
      remainingTime: 0,
      startTime: new Date(),
      metadata: downloadParams,
    };

    tasks.value.set(taskId, task);

    try {
      const api = getElectronAPI();

      // 将文件大小信息添加到下载参数中，以便 Electron 端使用
      const enhancedParams: Record<string, any> = {
        ...downloadParams,
      };

      if (fileSize !== undefined) {
        enhancedParams.expectedFileSize = fileSize; // 直接传递数值，避免不必要的转换
        console.log(`📊 传递给Electron的文件大小: ${fileSize} bytes (数值类型)`);
      } else {
        console.warn("无法获取文件大小，原始值:", fileItem.size);
      }

      // 构建完整的文件路径
      // 如果 savePath 是文件夹路径，需要与文件名组合
      let fullFilePath = config.savePath;
      if (config.savePath && !config.savePath.endsWith(fileItem.name)) {
        // 确保路径分隔符正确处理
        fullFilePath = config.savePath.endsWith("/") ? `${config.savePath}${fileItem.name}` : `${config.savePath}/${fileItem.name}`;
      }

      // 调用Electron的下载管理器
      const result = await api.download.createTask?.(fileItem.name, fullFilePath, enhancedParams);

      if (!result?.success || !result.taskId) {
        throw new Error(result?.error || "无法创建下载任务");
      }

      console.log("Electron任务创建成功:", result.taskId);

      // 关联Electron任务ID
      const updatedTask = { ...task, streamTaskId: result.taskId };
      tasks.value.set(taskId, updatedTask);

      // 开始下载
      await api.download.startDownload?.(result.taskId);

      return taskId;
    } catch (error) {
      // 更新任务状态为错误
      const errorTask = {
        ...task,
        status: "error" as DownloadStatus,
        error: error instanceof Error ? error.message : "下载失败",
        endTime: new Date(),
      };
      tasks.value.set(taskId, errorTask);
      throw error;
    }
  };

  /**
   * 获取文件夹下载任务列表
   * 使用新的 getDownloadTasks 接口获取文件夹内所有文件及其相对路径
   * @param folderId - 文件夹ID
   * @param categoryId - 分类ID
   * @returns 包含文件信息和相对路径的数组
   */
  const getFolderDownloadTasks = async (folderId: string, categoryId: string | number): Promise<Array<{ file: any; relativePath: string }>> => {
    console.log("获取文件夹下载任务列表:", folderId, categoryId);

    const response = await filesApi.getDownloadTasks(categoryId, folderId);

    if (response.code !== 0) {
      throw new Error(response.msg || "获取文件夹下载任务失败");
    }

    const tasks = response.data?.tasks || [];
    console.log(`获取到 ${tasks.length} 个下载任务`);

    const filesWithPaths: Array<{ file: any; relativePath: string }> = [];

    for (const task of tasks) {
      if (task.is_folder === 0) {
        // 只处理文件，跳过文件夹
        console.log(`� 添加文件: ${task.file_name} -> ${task.relative_path}`);

        // 将下载任务转换为兼容的文件格式
        const fileData = {
          id: task.id,
          file_name: task.file_name,
          size: task.file_size,
          file_sign: task.file_sign,
          is_folder: task.is_folder,
        };

        filesWithPaths.push({
          file: fileData,
          relativePath: task.relative_path,
        });
      }
    }

    console.log(`完成文件夹任务分析，共找到 ${filesWithPaths.length} 个文件`);
    return filesWithPaths;
  };

  /**
   * 批量下载文件夹
   * 获取文件夹内所有文件并创建批量下载任务，保持文件夹层级结构
   * @param folderItem - 要下载的文件夹项
   * @param config - 下载配置
   * @returns 返回批量任务ID
   */
  const downloadFolder = async (folderItem: FolderItemType, config: DownloadConfig): Promise<string> => {
    if (!isElectron.value) {
      throw new Error("下载功能仅在Electron环境中可用");
    }

    const batchId = generateUniqueId("batch");

    try {
      // 使用新的 getDownloadTasks 接口获取文件夹内所有文件（包含 relative_path）
      console.log("开始分析文件夹结构:", folderItem.name, folderItem.id);
      const filesWithPaths = await getFolderDownloadTasks(folderItem.id, config.category_id);
      const files = filesWithPaths.map(({ file }) => file);
      console.log(`文件夹分析完成: 共发现 ${files.length} 个文件`);

      // 详细显示找到的文件
      if (files.length > 0) {
        console.log("文件列表:", files.map((f: any) => f.file_name).join(", "));
      } else {
        console.log("文件夹为空或无法获取文件列表");
      }

      // 创建批量任务记录
      const batchTask: BatchDownloadTask = {
        id: batchId,
        type: "batch",
        batchName: folderItem.name,
        folderPath: folderItem.path || "",
        totalFiles: files.length,
        completedFiles: 0,
        failedFiles: 0,
        pausedFiles: 0,
        totalSize: files.reduce((sum: number, file: any) => sum + (file.size || 0), 0),
        downloadedSize: 0,
        progress: 0,
        status: "pending",
        startTime: new Date(),
        duration: 0,
        subTasks: [],
        expanded: false,
        avgDownloadSpeed: 0,
        estimatedRemainingTime: 0,
      };

      batchTasks.value.set(batchId, batchTask);

      if (files.length === 0) {
        // 空文件夹处理
        console.log("检测到空文件夹:", folderItem.name);
        const completedBatch = {
          ...batchTask,
          status: "completed" as DownloadStatus,
          progress: 100,
          endTime: new Date(),
          duration: 0,
          completedFiles: 0, // 确保显示正确的完成文件数
        };
        batchTasks.value.set(batchId, completedBatch);
        toast.success(`文件夹 "${folderItem.name}" 下载完成（空文件夹）`);
        console.log("空文件夹下载任务已完成:", folderItem.name);
        return batchId;
      }

      // 创建子任务
      const subTaskIds: string[] = [];
      const api = getElectronAPI();

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const fileWithPath = filesWithPaths[i];
        const subTaskId = generateUniqueId("download");
        const downloadParams = buildDownloadParams(file.id, config.category_id);

        // 优化路径构建逻辑，避免重复路径
        let fullSavePath: string;

        // 处理 relative_path，移除分类文件夹前缀，保留从目标文件夹开始的路径
        let cleanRelativePath = fileWithPath.relativePath;

        // 标准化路径分隔符
        cleanRelativePath = cleanRelativePath.replace(/\\/g, "/");
        const folderName = folderItem.name.replace(/\\/g, "/");

        console.log(`� 路径分析: 原始路径="${cleanRelativePath}", 目标文件夹="${folderName}"`);

        // 分析路径结构: "分类文件夹/目标文件夹/子路径/文件名"
        const pathParts = cleanRelativePath.split("/");
        const folderIndex = pathParts.findIndex((part) => part === folderName);

        // 重新设计路径处理逻辑
        if (folderIndex !== -1) {
          // 找到目标文件夹，提取从目标文件夹之后的内部路径
          const internalPath = pathParts.slice(folderIndex + 1).join("/");

          if (internalPath) {
            // 有内部路径，保持文件夹结构：目标文件夹/内部路径
            cleanRelativePath = `${folderName}/${internalPath}`;
          } else {
            // 没有内部路径，直接在目标文件夹内：目标文件夹/文件名
            cleanRelativePath = `${folderName}/${file.file_name}`;
          }
        } else {
          // 如果没有找到目标文件夹名称，可能是直接在分类文件夹内的文件
          // 检查是否是直接下载分类文件夹的情况
          if (pathParts.length === 2 && pathParts[0] === folderName) {
            // 场景3: 直接下载分类文件夹内的文件，只保留文件名
            cleanRelativePath = pathParts[1];
          } else if (pathParts.length > 1) {
            // 其他情况：移除第一个路径部分（通常是分类文件夹名称）
            cleanRelativePath = pathParts.slice(1).join("/");
          }
        }

        // 移除开头的斜杠（如果有的话）
        cleanRelativePath = cleanRelativePath.replace(/^\/+/, "");

        // 如果清理后的路径为空，使用文件名
        if (!cleanRelativePath) {
          cleanRelativePath = file.file_name;
        }

        if (config.savePath) {
          // 如果用户指定了保存路径，直接使用清理后的相对路径
          fullSavePath = `${config.savePath}/${cleanRelativePath}`.replace(/\/+/g, "/");
        } else {
          // 如果没有指定保存路径，根据情况构建路径
          if (cleanRelativePath === file.file_name) {
            // 场景3: 直接文件下载，不创建文件夹层级
            fullSavePath = cleanRelativePath;
          } else {
            // 其他情况：直接使用清理后的相对路径（已经包含了目标文件夹）
            fullSavePath = cleanRelativePath;
          }
        }

        // 创建子任务记录
        const subTask: DownloadTask = {
          id: subTaskId,
          fileName: fileWithPath.relativePath, // 使用完整的相对路径作为显示名称
          fileSize: file.size,
          progress: 0,
          status: "pending",
          bytesDownloaded: 0,
          downloadSpeed: 0,
          remainingTime: 0,
          startTime: new Date(),
          metadata: downloadParams,
          isSubTask: true,
          batchId,
        };

        tasks.value.set(subTaskId, subTask);
        subTaskIds.push(subTaskId);

        // 创建Electron下载任务，传递完整的保存路径和子任务信息
        const enhancedParams = {
          ...downloadParams,
          isSubTask: "true",
          batchId,
        };
        const result = await api.download.createTask?.(file.file_name, fullSavePath, enhancedParams);

        if (result?.success && result.taskId) {
          const updatedSubTask = { ...subTask, streamTaskId: result.taskId };
          tasks.value.set(subTaskId, updatedSubTask);
        }
      }

      // 更新批量任务的子任务列表
      const updatedBatch = { ...batchTask, subTasks: subTaskIds, status: "downloading" as DownloadStatus };
      batchTasks.value.set(batchId, updatedBatch);

      // 开始批量下载
      const streamTaskIds = subTaskIds.map((id) => tasks.value.get(id)?.streamTaskId).filter(Boolean) as string[];

      if (streamTaskIds.length > 0) {
        await api.download.startBatch?.(streamTaskIds);
      }

      return batchId;
    } catch (error) {
      // 更新批量任务状态为错误
      const errorBatch = {
        ...batchTasks.value.get(batchId)!,
        status: "error" as DownloadStatus,
        error: error instanceof Error ? error.message : "批量下载失败",
        endTime: new Date(),
      };
      batchTasks.value.set(batchId, errorBatch);
      throw error;
    }
  };

  /**
   * 下载项目（文件或文件夹）
   * 根据项目类型自动选择单文件下载或文件夹批量下载
   * @param item - 要下载的项目
   * @param config - 下载配置
   * @returns 返回任务ID
   */
  const downloadItem = async (item: ItemType, config: DownloadConfig): Promise<string> => {
    console.log("开始下载项目:", item.name, "类型:", item.type);

    if (item.type === "folder") {
      return await downloadFolder(item as FolderItemType, config);
    } else {
      return await downloadSingleFile(item as FileItemType, config);
    }
  };

  /**
   * 混合下载多个项目
   * 支持同时下载文件和文件夹的组合
   * @param items - 要下载的项目列表
   * @param config - 下载配置
   * @returns 返回任务ID列表
   */
  const downloadMultipleItems = async (items: ItemType[], config: DownloadConfig): Promise<string[]> => {
    console.log("开始混合下载:", items.length, "个项目");

    const folders = items.filter((item) => item.type === "folder") as FolderItemType[];
    const files = items.filter((item) => item.type !== "folder") as FileItemType[];

    console.log("混合下载分析:", folders.length, "个文件夹,", files.length, "个文件");

    try {
      // 创建所有下载任务的Promise数组，实现并行下载
      const downloadPromises: Promise<string>[] = [];

      // 添加文件夹下载任务
      folders.forEach((folder) => {
        console.log("准备下载文件夹:", folder.name);
        downloadPromises.push(downloadFolder(folder, config));
      });

      // 添加文件下载任务
      files.forEach((file) => {
        console.log("准备下载文件:", file.name);
        downloadPromises.push(downloadSingleFile(file, config));
      });

      // 并行执行所有下载任务
      const taskIds = await Promise.all(downloadPromises);

      console.log("混合下载任务创建完成:", taskIds.length, "个任务，已开始并行下载");
      return taskIds;
    } catch (error) {
      console.error("混合下载失败:", error);
      throw error;
    }
  };

  /**
   * 暂停下载任务
   * @param taskId - 任务ID
   * @returns 是否成功暂停
   */
  const pauseDownload = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.streamTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.download.pauseDownload?.(task.streamTaskId);
      return result?.success || false;
    } catch (error) {
      console.error("暂停下载失败:", error);
      return false;
    }
  };

  /**
   * 恢复下载任务
   * @param taskId - 任务ID
   * @returns 是否成功恢复
   */
  const resumeDownload = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.streamTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.download.resumeDownload?.(task.streamTaskId);
      return result?.success || false;
    } catch (error) {
      console.error("恢复下载失败:", error);
      return false;
    }
  };

  /**
   * 取消下载任务
   * @param taskId - 任务ID
   * @returns 是否成功取消
   */
  const cancelDownload = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.streamTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.download.cancelDownload?.(task.streamTaskId);

      if (result?.success) {
        // 更新本地任务状态
        const updatedTask = {
          ...task,
          status: "cancelled" as DownloadStatus,
          endTime: new Date(),
        };
        tasks.value.set(taskId, updatedTask);

        if (task.batchId) {
          updateBatchTaskProgress(task.batchId);
        }
      }

      return result?.success || false;
    } catch (error) {
      console.error("取消下载失败:", error);
      return false;
    }
  };

  /**
   * 重试下载任务
   * @param taskId - 任务ID
   * @returns 是否成功重试
   */
  const retryDownload = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.streamTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.download.retryDownload?.(task.streamTaskId);

      if (result?.success) {
        // 更新本地任务状态
        const updatedTask = {
          ...task,
          status: "pending" as DownloadStatus,
          error: undefined,
          endTime: undefined,
        };
        tasks.value.set(taskId, updatedTask);

        if (task.batchId) {
          updateBatchTaskProgress(task.batchId);
        }
      }

      return result?.success || false;
    } catch (error) {
      console.error("重试下载失败:", error);
      return false;
    }
  };

  /**
   * 删除下载任务
   * 如果任务正在进行中，会先取消再删除
   * @param taskId - 任务ID
   */
  const removeTask = async (taskId: string): Promise<void> => {
    const task = tasks.value.get(taskId);
    if (task) {
      // 如果任务还在进行中，先取消它
      if (["pending", "downloading", "paused"].includes(task.status)) {
        console.warn("任务还在进行中，先取消再删除:", task.fileName);
        await cancelDownload(taskId);
      }

      // 从 Electron 端删除任务
      if (task.streamTaskId) {
        try {
          const api = getElectronAPI();
          await api.download.deleteTask?.(task.streamTaskId);
          console.log("已从 Electron 端删除任务:", task.fileName);
        } catch (error) {
          console.error("从 Electron 端删除任务失败:", error);
        }
      }

      // 从前端删除任务
      tasks.value.delete(taskId);
      console.log("已从前端删除任务:", task.fileName);

      if (task.batchId) {
        updateBatchTaskProgress(task.batchId);
      }
    }
  };

  /**
   * 暂停批量下载任务
   * @param batchId - 批量任务ID
   * @returns 是否成功暂停
   */
  const pauseBatchDownload = async (batchId: string): Promise<boolean> => {
    const batchTask = batchTasks.value.get(batchId);
    if (!batchTask) return false;

    try {
      const api = getElectronAPI();
      const streamTaskIds = batchTask.subTasks.map((taskId) => tasks.value.get(taskId)?.streamTaskId).filter(Boolean) as string[];

      const result = await api.download.pauseBatch?.(streamTaskIds);
      return result?.success || false;
    } catch (error) {
      console.error("暂停批量下载失败:", error);
      return false;
    }
  };

  /**
   * 恢复批量下载任务
   * @param batchId - 批量任务ID
   * @returns 是否成功恢复
   */
  const resumeBatchDownload = async (batchId: string): Promise<boolean> => {
    const batchTask = batchTasks.value.get(batchId);
    if (!batchTask) return false;

    try {
      const api = getElectronAPI();
      const streamTaskIds = batchTask.subTasks.map((taskId) => tasks.value.get(taskId)?.streamTaskId).filter(Boolean) as string[];

      // 使用专门的批量恢复API而不是批量开始API
      const result = await api.download.resumeBatch?.(streamTaskIds);
      return result?.success || false;
    } catch (error) {
      console.error("恢复批量下载失败:", error);
      return false;
    }
  };

  /**
   * 取消批量下载任务
   * @param batchId - 批量任务ID
   * @returns 是否成功取消
   */
  const cancelBatchDownload = async (batchId: string): Promise<boolean> => {
    const batchTask = batchTasks.value.get(batchId);
    if (!batchTask) return false;

    try {
      let allSuccess = true;

      // 取消所有子任务
      for (const taskId of batchTask.subTasks) {
        const success = await cancelDownload(taskId);
        if (!success) allSuccess = false;
      }

      if (allSuccess) {
        // 更新批量任务状态
        const updatedBatch = {
          ...batchTask,
          status: "cancelled" as DownloadStatus,
          endTime: new Date(),
        };
        batchTasks.value.set(batchId, updatedBatch);
      }

      return allSuccess;
    } catch (error) {
      console.error("取消批量下载失败:", error);
      return false;
    }
  };

  /**
   * 删除批量下载任务
   * 同时删除所有相关的子任务
   * @param batchId - 批量任务ID
   */
  const removeBatchTask = (batchId: string): void => {
    const batchTask = batchTasks.value.get(batchId);
    if (batchTask) {
      // 移除所有子任务
      batchTask.subTasks.forEach((taskId) => {
        tasks.value.delete(taskId);
      });

      // 移除批量任务
      batchTasks.value.delete(batchId);
    }
  };

  /**
   * 清空已完成的任务
   * 删除所有状态为完成、错误或取消的任务
   * @returns 是否成功清空
   */
  const clearCompletedTasks = async (): Promise<boolean> => {
    try {
      const api = getElectronAPI();
      const result = await api.download.clearCompletedTasks?.();

      if (result?.success) {
        // 同时清空前端的已完成任务
        const completedTaskIds: string[] = [];

        tasks.value.forEach((task, taskId) => {
          if (["completed", "error", "cancelled"].includes(task.status)) {
            completedTaskIds.push(taskId);
          }
        });

        batchTasks.value.forEach((batchTask, batchId) => {
          if (["completed", "error", "cancelled"].includes(batchTask.status)) {
            completedTaskIds.push(batchId);
          }
        });

        completedTaskIds.forEach((taskId) => {
          tasks.value.delete(taskId);
          batchTasks.value.delete(taskId);
        });

        console.log(`已清空 ${completedTaskIds.length} 个已完成的下载任务`);
        return true;
      }

      return false;
    } catch (error) {
      console.error("清空已完成任务失败:", error);
      return false;
    }
  };

  /**
   * 清空所有任务
   * 删除所有下载任务，包括正在进行的任务
   * @returns 是否成功清空
   */
  const clearAllTasks = async (): Promise<boolean> => {
    try {
      console.log("清空所有下载任务");

      const api = getElectronAPI();
      const result = await api.download.clearAllTasks();

      if (result?.success) {
        // 清空前端的所有任务
        tasks.value.clear();
        batchTasks.value.clear();

        console.log("已清空所有下载任务");
        return true;
      }

      return false;
    } catch (error) {
      console.error("清空所有任务失败:", error);
      return false;
    }
  };

  /**
   * 清理重复任务
   * 检查并删除相同文件名的重复任务，保留最新的任务
   */
  const cleanupDuplicateTasks = () => {
    const tasksByFileName = new Map<string, DownloadTask[]>();

    // 按文件名分组任务
    allTasks.value.forEach((task) => {
      const fileName = task.fileName;
      if (!tasksByFileName.has(fileName)) {
        tasksByFileName.set(fileName, []);
      }
      tasksByFileName.get(fileName)!.push(task);
    });

    // 检查并清理重复任务
    tasksByFileName.forEach((tasksWithSameName) => {
      if (tasksWithSameName.length > 1) {
        // 保留最新的任务，删除其他的
        const sortedTasks = tasksWithSameName.sort((a, b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime());

        const tasksToRemove = sortedTasks.slice(1);

        tasksToRemove.forEach((task) => {
          tasks.value.delete(task.id);
        });
      }
    });
  };

  /**
   * 暂停解压缩任务
   * @param taskId - 任务ID
   * @returns 是否成功暂停
   */
  const pauseExtraction = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.extractionTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.extraction?.pauseExtraction?.(task.extractionTaskId);
      return result?.success || false;
    } catch (error) {
      console.error("暂停解压缩失败:", error);
      return false;
    }
  };

  /**
   * 恢复解压缩任务
   * @param taskId - 任务ID
   * @returns 是否成功恢复
   */
  const resumeExtraction = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.extractionTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.extraction?.resumeExtraction?.(task.extractionTaskId);
      return result?.success || false;
    } catch (error) {
      console.error("恢复解压缩失败:", error);
      return false;
    }
  };

  /**
   * 取消解压缩任务
   * @param taskId - 任务ID
   * @returns 是否成功取消
   */
  const cancelExtraction = async (taskId: string): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task?.extractionTaskId) return false;

    try {
      const api = getElectronAPI();
      const result = await api.extraction?.cancelExtraction?.(task.extractionTaskId);

      if (result?.success) {
        // 更新本地任务状态
        const updatedTask = {
          ...task,
          extractionStatus: "cancelled" as ExtractionStatus,
          status: "completed" as DownloadStatus, // 下载已完成，只是解压缩被取消
        };
        tasks.value.set(taskId, updatedTask);
      }

      return result?.success || false;
    } catch (error) {
      console.error("取消解压缩失败:", error);
      return false;
    }
  };

  /**
   * 手动触发解压缩
   * @param taskId - 任务ID
   * @param extractPath - 解压路径（可选）
   * @param deleteAfterExtraction - 解压完成后是否删除原文件（可选）
   * @returns 是否成功开始解压缩
   */
  const startExtraction = async (taskId: string, extractPath?: string, deleteAfterExtraction?: boolean): Promise<boolean> => {
    const task = tasks.value.get(taskId);
    if (!task || task.status !== "completed") {
      console.warn("只能对已完成下载的任务进行解压缩");
      return false;
    }

    if (!is7zFile(task.fileName)) {
      console.warn("只支持7z文件的解压缩");
      return false;
    }

    try {
      const api = getElectronAPI();

      // 创建解压缩任务
      const result = await api.extraction?.createTask?.(
        task.metadata?.filePath || task.fileName, // 使用实际文件路径
        extractPath,
        {
          downloadTaskId: taskId,
          deleteAfterExtraction: deleteAfterExtraction ?? task.deleteAfterExtraction,
        }
      );

      if (!result?.success || !result.taskId) {
        throw new Error(result?.error || "无法创建解压缩任务");
      }

      // 更新任务状态
      const updatedTask = {
        ...task,
        needsExtraction: true,
        extractionTaskId: result.taskId,
        extractionStatus: "pending" as ExtractionStatus,
        status: "extracting" as DownloadStatus,
        deleteAfterExtraction: deleteAfterExtraction ?? task.deleteAfterExtraction,
      };
      tasks.value.set(taskId, updatedTask);

      // 开始解压缩
      await api.extraction?.startExtraction?.(result.taskId);

      return true;
    } catch (error) {
      console.error("开始解压缩失败:", error);
      return false;
    }
  };

  /**
   * 调试方法：检查任务状态
   * 输出所有任务的详细状态信息，用于调试
   */
  const debugTaskStatus = () => {
    console.log("当前任务状态:");
    allTasks.value.forEach((task) => {
      console.log(`- ${task.fileName}: 进度=${task.progress}%, 大小=${task.fileSize}, 已下载=${task.bytesDownloaded}, 状态=${task.status}`);
      if (task.needsExtraction) {
        console.log(`  解压缩: 状态=${task.extractionStatus}, 进度=${task.extractionProgress}%`);
      }
    });
  };

  return {
    // 状态
    tasks: allTasks,
    standaloneTasks,
    activeTasks,
    completedTasks,
    errorTasks,
    batchTasks: allBatchTasks,
    activeBatchTasks,
    totalProgress,
    isElectron,

    // 内部状态（用于同步）
    tasksMap: tasks,
    batchTasksMap: batchTasks,

    // 核心方法
    downloadItem,
    downloadSingleFile,
    downloadFolder,
    downloadMultipleItems,
    setupEventListeners,

    // 任务控制方法
    pauseDownload,
    resumeDownload,
    cancelDownload,
    retryDownload,
    removeTask,
    clearCompletedTasks,
    clearAllTasks,

    // 批量任务控制方法
    pauseBatchDownload,
    resumeBatchDownload,
    cancelBatchDownload,
    removeBatchTask,

    // 解压缩控制方法
    startExtraction,
    pauseExtraction,
    resumeExtraction,
    cancelExtraction,

    // 事件处理器
    handleTaskCreated,
    handleTaskProgress,
    handleTaskStatusChanged,
    handleTaskCompleted,
    handleTaskError,

    // 工具方法
    generateUniqueId,
    buildDownloadParams,
    getElectronAPI,
    mapElectronStatusToDownloadStatus,
    updateBatchTaskProgress,

    // 手动恢复任务（用于调试）
    restoreExistingTasks,
    cleanupDuplicateTasks,
    debugTaskStatus,
  };
}
