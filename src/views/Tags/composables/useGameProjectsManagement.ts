import { ref, reactive } from "vue";
import { toast } from "vue-sonner";
import tagsService, { type GameProject, type GameItem, type CreateGameProjectParams, type UpdateGameProjectParams } from "@/api/services/tags";

export interface GameProjectsManagementState {
  gameProjects: GameProject[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
}

export function useGameProjectsManagement() {
  // 响应式状态
  const state = reactive<GameProjectsManagementState>({
    gameProjects: [],
    loading: false,
    error: null,
    searchQuery: "",
  });

  // 编辑状态
  const editingProject = ref<GameProject | null>(null);
  const isEditDialogOpen = ref(false);
  const isCreateDialogOpen = ref(false);

  // 获取游戏项目列表
  const fetchGameProjects = async () => {
    try {
      state.loading = true;
      state.error = null;

      const response = await tagsService.gameProjects.getGameProjectsList();

      if (response.code === 0 && response.data) {
        // 将API返回的GameItem转换为GameProject格式
        let gameProjects: GameProject[] = response.data.map((item: GameItem) => ({
          id: item.id,
          name: item.game_name,
          updatedBy: item.update_user,
          updatedAt: item.update_time,
        }));

        // 前端搜索过滤
        if (state.searchQuery) {
          const query = state.searchQuery.toLowerCase();
          gameProjects = gameProjects.filter((project) => project.name.toLowerCase().includes(query) || project.updatedBy.toLowerCase().includes(query));
        }

        state.gameProjects = gameProjects;
      } else {
        throw new Error(response.msg || "获取游戏项目列表失败");
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : "获取游戏项目列表失败";
      toast.error(state.error);
    } finally {
      state.loading = false;
    }
  };

  // 创建游戏项目
  const createGameProject = async (data: CreateGameProjectParams) => {
    try {
      state.loading = true;
      const response = await tagsService.gameProjects.createGameProject(data);

      if (response.code === 0) {
        toast.success("游戏项目创建成功");
        isCreateDialogOpen.value = false;
        await fetchGameProjects(); // 刷新列表
      } else {
        throw new Error(response.msg || "创建游戏项目失败");
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "创建游戏项目失败";
      toast.error(message);
      throw error;
    } finally {
      state.loading = false;
    }
  };

  // 更新游戏项目
  const updateGameProject = async (data: UpdateGameProjectParams) => {
    try {
      state.loading = true;
      const response = await tagsService.gameProjects.updateGameProject(data);

      if (response.code === 0) {
        toast.success("游戏项目更新成功");
        isEditDialogOpen.value = false;
        editingProject.value = null;
        await fetchGameProjects(); // 刷新列表
      } else {
        throw new Error(response.msg || "更新游戏项目失败");
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "更新游戏项目失败";
      toast.error(message);
      throw error;
    } finally {
      state.loading = false;
    }
  };

  // 删除单个游戏项目
  const deleteGameProject = async (id: string | number) => {
    try {
      state.loading = true;
      const response = await tagsService.gameProjects.deleteGameProject(id);

      if (response.code === 0) {
        toast.success("游戏项目删除成功");
        await fetchGameProjects(); // 刷新列表
      } else {
        throw new Error(response.msg || "删除游戏项目失败");
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "删除游戏项目失败";
      toast.error(message);
    } finally {
      state.loading = false;
    }
  };

  // 搜索
  const search = (query: string) => {
    state.searchQuery = query;
    fetchGameProjects();
  };

  // 编辑操作
  const openEditDialog = (project: GameProject) => {
    editingProject.value = { ...project };
    isEditDialogOpen.value = true;
  };

  const closeEditDialog = () => {
    editingProject.value = null;
    isEditDialogOpen.value = false;
  };

  const openCreateDialog = () => {
    isCreateDialogOpen.value = true;
  };

  const closeCreateDialog = () => {
    isCreateDialogOpen.value = false;
  };

  // 刷新数据
  const refresh = () => {
    fetchGameProjects();
  };

  return {
    // 状态
    state,
    editingProject,
    isEditDialogOpen,
    isCreateDialogOpen,

    // 方法
    fetchGameProjects,
    createGameProject,
    updateGameProject,
    deleteGameProject,
    search,
    openEditDialog,
    closeEditDialog,
    openCreateDialog,
    closeCreateDialog,
    refresh,
  };
}
