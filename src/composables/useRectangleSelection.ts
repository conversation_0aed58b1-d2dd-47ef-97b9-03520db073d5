import { ref, onMounted, onUnmounted, type Ref } from "vue";

export interface SelectionRect {
  startX: number;
  startY: number;
  endX: number;
  endY: number;
}

export interface UseRectangleSelectionOptions {
  containerRef: Ref<HTMLElement | null>;
  itemSelector: string;
  onSelectionChange?: (selectedIds: Set<string>) => void;
  getItemId?: (element: HTMLElement) => string | null;
}

export function useRectangleSelection(options: UseRectangleSelectionOptions) {
  const { containerRef, itemSelector, onSelectionChange, getItemId } = options;

  const isSelecting = ref(false);
  const selectionRect = ref<SelectionRect | null>(null);
  const selectedItems = ref<Set<string>>(new Set());

  let startX = 0;
  let startY = 0;
  let selectionDiv: HTMLElement | null = null;

  // 创建选择框元素
  const createSelectionDiv = () => {
    selectionDiv = document.createElement("div");
    selectionDiv.className = "rectangle-selection";
    selectionDiv.style.cssText = `
      position: absolute;
      border: 2px solid hsl(var(--primary));
      background-color: hsl(var(--primary) / 0.1);
      pointer-events: none;
      z-index: 1000;
      display: none;
    `;
    return selectionDiv;
  };

  // 获取元素相对于容器的位置
  const getRelativeRect = (element: HTMLElement, container: HTMLElement) => {
    const elementRect = element.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();

    return {
      left: elementRect.left - containerRect.left + container.scrollLeft,
      top: elementRect.top - containerRect.top + container.scrollTop,
      right: elementRect.right - containerRect.left + container.scrollLeft,
      bottom: elementRect.bottom - containerRect.top + container.scrollTop,
      width: elementRect.width,
      height: elementRect.height,
    };
  };

  // 检查矩形是否相交
  const isRectIntersecting = (rect1: SelectionRect, rect2: any) => {
    const left = Math.min(rect1.startX, rect1.endX);
    const right = Math.max(rect1.startX, rect1.endX);
    const top = Math.min(rect1.startY, rect1.endY);
    const bottom = Math.max(rect1.startY, rect1.endY);

    return !(right < rect2.left || left > rect2.right || bottom < rect2.top || top > rect2.bottom);
  };

  // 更新选择框位置和大小
  const updateSelectionRect = (currentX: number, currentY: number) => {
    if (!selectionDiv || !containerRef.value) return;

    const rect = {
      startX,
      startY,
      endX: currentX,
      endY: currentY,
    };

    const left = Math.min(rect.startX, rect.endX);
    const top = Math.min(rect.startY, rect.endY);
    const width = Math.abs(rect.endX - rect.startX);
    const height = Math.abs(rect.endY - rect.startY);

    selectionDiv.style.left = `${left}px`;
    selectionDiv.style.top = `${top}px`;
    selectionDiv.style.width = `${width}px`;
    selectionDiv.style.height = `${height}px`;
    selectionDiv.style.display = "block";

    selectionRect.value = rect;

    // 查找被选中的项目
    findSelectedItems(rect);
  };

  // 查找被框选的项目
  const findSelectedItems = (rect: SelectionRect) => {
    if (!containerRef.value) return;

    const items = containerRef.value.querySelectorAll(itemSelector);
    const newSelectedItems = new Set<string>();

    items.forEach((item) => {
      const element = item as HTMLElement;
      const itemRect = getRelativeRect(element, containerRef.value!);

      if (isRectIntersecting(rect, itemRect)) {
        const itemId = getItemId ? getItemId(element) : element.dataset.id;
        if (itemId) {
          newSelectedItems.add(itemId);
        }
      }
    });

    selectedItems.value = newSelectedItems;
    onSelectionChange?.(new Set(newSelectedItems));
  };

  // 鼠标按下开始选择
  const handleMouseDown = (event: MouseEvent) => {
    // 只处理左键点击，并且不是在项目上点击
    if (event.button !== 0) return;

    const target = event.target as HTMLElement;
    // 如果点击在项目上，不开始框选
    if (target.closest(itemSelector)) return;
    // 如果点击在复选框或按钮上，不开始框选
    if (target.closest('input, button, [role="button"]')) return;

    if (!containerRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    startX = event.clientX - containerRect.left + containerRef.value.scrollLeft;
    startY = event.clientY - containerRect.top + containerRef.value.scrollTop;

    isSelecting.value = true;

    if (!selectionDiv) {
      createSelectionDiv();
    }

    if (selectionDiv && containerRef.value) {
      containerRef.value.appendChild(selectionDiv);
    }

    // 阻止默认行为和事件冒泡
    event.preventDefault();
    event.stopPropagation();
  };

  // 鼠标移动更新选择框
  const handleMouseMove = (event: MouseEvent) => {
    if (!isSelecting.value || !containerRef.value) return;

    const containerRect = containerRef.value.getBoundingClientRect();
    const currentX = event.clientX - containerRect.left + containerRef.value.scrollLeft;
    const currentY = event.clientY - containerRect.top + containerRef.value.scrollTop;

    updateSelectionRect(currentX, currentY);

    event.preventDefault();
  };

  // 鼠标释放结束选择
  const handleMouseUp = () => {
    if (!isSelecting.value) return;

    isSelecting.value = false;
    selectionRect.value = null;

    if (selectionDiv) {
      selectionDiv.style.display = "none";
      if (selectionDiv.parentNode) {
        selectionDiv.parentNode.removeChild(selectionDiv);
      }
    }
  };

  // 清空选择
  const clearSelection = () => {
    selectedItems.value.clear();
    onSelectionChange?.(new Set());
  };

  // 设置选择状态
  const setSelectedItems = (items: Set<string>) => {
    selectedItems.value = new Set(items);
  };

  onMounted(() => {
    if (containerRef.value) {
      containerRef.value.addEventListener("mousedown", handleMouseDown);
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);
    }
  });

  onUnmounted(() => {
    if (containerRef.value) {
      containerRef.value.removeEventListener("mousedown", handleMouseDown);
    }
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);

    if (selectionDiv && selectionDiv.parentNode) {
      selectionDiv.parentNode.removeChild(selectionDiv);
    }
  });

  return {
    isSelecting,
    selectionRect,
    selectedItems,
    clearSelection,
    setSelectedItems,
  };
}
