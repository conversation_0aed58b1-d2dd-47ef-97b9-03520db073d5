<template>
  <Teleport to="body">
    <div v-if="show" :style="{ top: y + 'px', left: x + 'px' }"
      class="fixed z-50 py-1 border rounded-lg shadow-lg bg-popover border-border min-w-[140px]" @click.stop>
      <button v-for="action in actions" :key="action.label" @click="handleActionClick(action)"
        class="flex items-center px-4 py-2 w-full text-left transition-colors hover:bg-accent" :class="{
          'text-destructive hover:bg-destructive/10': action.destructive,
          'opacity-50 cursor-not-allowed disabled:hover:bg-transparent': action.disabled
        }" :disabled="action.disabled">
        <component :is="action.icon" class="mr-2 w-4 h-4" />
        {{ action.label }}
      </button>
    </div>
  </Teleport>

  <!-- 文件夹选择对话框 -->
  <FolderPickerDialog v-model:open="showFolderPicker" :category-id="String(route.query.categoryId)"
    :current-parent-id="String(route.query.parentId)" @confirm="handleFolderSelectedWrapper" @cancel="cancelMove" />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { DownloadIcon, TrashIcon, Pencil, FolderOpen, Move } from 'lucide-vue-next'
import { useRoute } from 'vue-router'
import { useFileDelete } from '@/composables/useFileDelete'
import { useFileMove } from '@/composables/useFileMove'
import { FolderPickerDialog } from '@/components/FolderPicker'
import type { ItemType, FolderItemType } from '@/types/files'

// Props
const props = defineProps<{
  show: boolean
  x: number
  y: number
  item: ItemType | null
  selectedItems?: ItemType[]  // 新增：当前选中的文件列表
}>()

// Emits
const emit = defineEmits<{
  close: []
  openFolder: [item: FolderItemType]
  download: [item: ItemType]
  batchDownload: [items: ItemType[]]  // 新增：批量下载事件
  rename: [item: ItemType]
  share: [item: ItemType]
  deleteSuccess: [item: ItemType]
  batchDeleteSuccess: [items: ItemType[]]  // 新增：批量删除成功事件
  refreshDirectory: []
}>()

// 获取路由信息
const route = useRoute()

// 使用删除功能
const { isDeleting, deleteFiles } = useFileDelete(String(route.query.categoryId))

// 使用移动功能
const { isMoving, showFolderPicker, startMove, handleFolderSelected, cancelMove } = useFileMove(String(route.query.categoryId))

// 计算实际要操作的文件列表
const getOperationItems = (): ItemType[] => {
  // 如果有多个文件选中，且右键点击的文件在选中列表中，则操作所有选中的文件
  if (props.selectedItems && props.selectedItems.length > 1 && props.item) {
    const isClickedItemSelected = props.selectedItems.some(item => item.id === props.item!.id)
    if (isClickedItemSelected) {
      return props.selectedItems
    }
  }
  // 否则只操作右键点击的文件
  return props.item ? [props.item] : []
}

// 菜单操作定义
interface MenuAction {
  label: string
  icon: any
  handler: string
  destructive?: boolean
  disabled?: boolean
}

// 计算可用的菜单操作
const actions = computed<MenuAction[]>(() => {
  if (!props.item) return []

  const baseActions: MenuAction[] = [
    { label: '下载', icon: DownloadIcon, handler: 'download' },
    {
      label: isMoving.value ? '移动中...' : '移动',
      icon: Move,
      handler: 'move',
      disabled: isMoving.value
    },
    { label: '重命名', icon: Pencil, handler: 'rename' },
    {
      label: isDeleting.value ? '删除中...' : '删除',
      icon: TrashIcon,
      handler: 'delete',
      destructive: true,
      disabled: isDeleting.value
    }
  ]

  // 如果是文件夹，添加打开操作
  if (props.item.type === 'folder') {
    return [
      { label: '打开', icon: FolderOpen, handler: 'openFolder' },
      ...baseActions
    ]
  }

  return baseActions
})

// 处理文件夹选择确认
const handleFolderSelectedWrapper = async (targetDirId: string) => {
  const success = await handleFolderSelected(targetDirId)
  if (success) {
    emit('refreshDirectory')
  }
}

// 处理操作点击
const handleActionClick = async (action: MenuAction) => {
  if (!props.item || action.disabled) return

  const operationItems = getOperationItems()

  switch (action.handler) {
    case 'openFolder':
      emit('openFolder', props.item as FolderItemType)
      break
    case 'download':
      if (operationItems.length > 1) {
        emit('batchDownload', operationItems)
      } else {
        emit('download', operationItems[0])
      }
      break
    case 'move':
      // 开始移动流程
      if (operationItems.length > 1) {
        startMove(operationItems)
      } else {
        startMove(operationItems[0])
      }
      return // 不关闭菜单，等待文件夹选择完成
    case 'rename':
      emit('rename', props.item)
      break
    case 'share':
      emit('share', props.item)
      break
    case 'delete':
      // 执行删除操作
      if (operationItems.length > 1) {
        const success = await deleteFiles(operationItems)
        if (success) {
          emit('batchDeleteSuccess', operationItems)
          emit('refreshDirectory')
        }
      } else {
        const success = await deleteFiles(operationItems[0])
        if (success) {
          emit('deleteSuccess', operationItems[0])
          emit('refreshDirectory')
        }
      }
      break
  }

  emit('close')
}
</script>
