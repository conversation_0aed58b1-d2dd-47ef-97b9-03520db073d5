<template>
  <div v-if="selectedCount > 0" class="flex gap-2 items-center" :class="containerClass">
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button size="sm" :disabled="isDownloading" @click.stop="handleDownload">
            <Download class="w-4 h-4" /> {{ isDownloading ? '下载中...' : '下载' }}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ isDownloading ? '下载中...' : `下载 (${selectedCount} 项)` }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button variant="outline" size="sm" :disabled="isMoving" @click.stop="handleMove">
            <Move class="w-4 h-4" /> {{ isMoving ? '移动中...' : '移动' }}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ isMoving ? '移动中...' : `移动 (${selectedCount} 项)` }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>

    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger as-child>
          <Button variant="destructive" size="sm" :disabled="isDeleting" @click.stop="handleDelete">
            <Trash2 class="w-4 h-4" /> {{ isDeleting ? '删除中...' : '删除' }}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>{{ isDeleting ? '删除中...' : `删除 (${selectedCount} 项)` }}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  </div>

  <!-- 文件夹选择对话框 -->
  <FolderPickerDialog v-model:open="showFolderPicker" :category-id="String(route.query.categoryId)"
    :current-parent-id="String(route.query.parentId)" @confirm="handleFolderSelectedWrapper" @cancel="cancelMove" />
</template>

<script setup lang="ts">
import { Download, Move, Trash2 } from 'lucide-vue-next'
import { useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useFileDelete } from '@/composables/useFileDelete'
import { useFileMove } from '@/composables/useFileMove'
import { FolderPickerDialog } from '@/components/FolderPicker'
import type { ItemType } from '@/types/files'

// Props
const props = defineProps<{
  selectedCount: number
  containerClass?: string
  isDownloading?: boolean
  selectedItems?: ItemType[]
}>()

// Emits
const emit = defineEmits<{
  download: []
  move: []
  delete: []
  deleteSuccess: [deletedItems: ItemType[]]
  refreshDirectory: []
}>()

// 获取路由信息
const route = useRoute()

// 使用删除功能
const { isDeleting, deleteFiles } = useFileDelete(String(route.query.categoryId))

// 使用移动功能
const { isMoving, showFolderPicker, startMove, handleFolderSelected, cancelMove } = useFileMove(String(route.query.categoryId))

// 事件处理
const handleDownload = () => {
  emit('download')
}

const handleMove = () => {
  // 如果有传入选中的项目，开始移动流程
  if (props.selectedItems && props.selectedItems.length > 0) {
    startMove(props.selectedItems)
  }
}

// 处理文件夹选择确认
const handleFolderSelectedWrapper = async (targetDirId: string) => {
  const success = await handleFolderSelected(targetDirId)
  if (success) {
    emit('refreshDirectory')
  }
}

const handleDelete = async () => {
  // 如果有传入选中的项目，直接删除
  if (props.selectedItems && props.selectedItems.length > 0) {
    const success = await deleteFiles(props.selectedItems)
    if (success) {
      emit('deleteSuccess', props.selectedItems)
      emit('refreshDirectory')
    }
  }
}
</script>