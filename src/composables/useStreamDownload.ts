import { computed, type ComputedRef } from "vue";
import { useStreamDownloadManager } from "@/composables/useStreamDownloadManager";
import type { ItemType } from "@/types/files";

// 下载配置
export interface DownloadConfig {
  savePath?: string;
  category_id: number | string;
}

export function useStreamDownload(downloadConfig: DownloadConfig | ComputedRef<DownloadConfig>) {
  const downloadManager = useStreamDownloadManager();

  // 确保配置是响应式的
  const config = computed(() => {
    return typeof downloadConfig === "object" && "value" in downloadConfig ? downloadConfig.value : downloadConfig;
  });

  // 计算属性 - 直接使用下载管理器的状态
  const downloadTasks = computed(() => downloadManager.tasks.value);
  const downloadingTasks = computed(() => downloadManager.activeTasks.value);
  const hasActiveDownloads = computed(() => downloadManager.activeTasks.value.length > 0);

  /**
   * 下载项目（文件或文件夹）
   */
  const downloadItem = async (item: ItemType): Promise<string> => {
    return await downloadManager.downloadItem(item, config.value);
  };

  /**
   * 批量下载项目（使用优化的混合下载策略）
   */
  const downloadMultipleItems = async (items: ItemType[]): Promise<string[]> => {
    console.log(`🚀 开始混合下载 ${items.length} 个项目`);

    // 使用下载管理器的优化混合下载策略
    return await downloadManager.downloadMultipleItems(items, config.value);
  };

  /**
   * 取消下载任务
   */
  const cancelDownload = async (taskId: string): Promise<void> => {
    await downloadManager.cancelDownload(taskId);
  };

  /**
   * 清除已完成的任务
   */
  const clearCompletedTasks = (): void => {
    // 由下载管理器处理，这里保留接口兼容性
  };

  /**
   * 获取任务信息
   */
  const getTask = (taskId: string) => {
    return downloadManager.tasksMap.value.get(taskId);
  };

  return {
    // 状态
    downloadTasks,
    downloadingTasks,
    hasActiveDownloads,

    // 方法
    downloadItem,
    downloadMultipleItems,
    cancelDownload,
    clearCompletedTasks,
    getTask,

    // 工具方法
    updateTaskStatus: () => {}, // 保留接口兼容性，实际由下载管理器处理
  };
}
