/**
 * 压缩任务状态
 */
export type ArchiveStatus = "pending" | "compressing" | "completed" | "error" | "cancelled";

/**
 * 压缩任务接口
 */
export interface ArchiveTask {
  id: string;
  name: string;
  sourcePaths: string[];
  outputPath: string;
  status: ArchiveStatus;
  progress: number;
  totalFiles: number;
  processedFiles: number;
  currentFile?: string;
  startTime: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * 压缩配置接口
 */
export interface ArchiveConfig {
  compressionLevel: number; // 0-9, 0为仅存储
  format: "7z" | "zip";
  tempDir: string;
  maxConcurrent: number;
}

/**
 * 压缩选项接口
 */
export interface ArchiveOptions {
  name?: string;
  preserveStructure?: boolean;
  compressionLevel?: number;
  format?: "7z" | "zip";
  metadata?: Record<string, any>;
}

/**
 * 压缩结果接口
 */
export interface ArchiveResult {
  success: boolean;
  archivePath?: string;
  error?: string;
  stats?: {
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    fileCount: number;
    duration: number;
  };
}

/**
 * API响应接口
 */
export interface ArchiveApiResponse {
  success: boolean;
  taskId?: string;
  archivePath?: string;
  error?: string;
  data?: any;
}

/**
 * 压缩事件接口
 */
export interface ArchiveEvents {
  "task-created": (taskId: string, task: ArchiveTask) => void;
  "task-progress": (taskId: string, progress: number, currentFile?: string) => void;
  "task-completed": (taskId: string, result: ArchiveResult) => void;
  "task-error": (taskId: string, error: string) => void;
  "task-cancelled": (taskId: string) => void;
}

/**
 * 智能打包检测结果
 */
export interface SmartPackingAnalysis {
  shouldPack: boolean;
  reason: string;
  fileCount: number;
  totalSize: number;
  estimatedPackTime: number;
  estimatedSavings: {
    uploadTime: number;
    bandwidth: number;
  };
}

/**
 * 批量打包选项
 */
export interface BatchPackingOptions {
  threshold: number; // 文件数量阈值
  maxSize?: number; // 最大文件大小限制（可选，已移除限制）
  skipConfirmation?: boolean;
  autoCleanup?: boolean;
}
