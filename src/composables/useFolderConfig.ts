import { Home, Folder, Box, Globe, Play, Sparkles, Music, Image, FileImage, Video, Gamepad2, Paintbrush, Type, FileIcon, Trash2 } from "lucide-vue-next";

// 文件夹类型定义
export interface FolderConfig {
  name: string;
  icon: any;
  iconColor: string;
  description?: string;
  fileType?: string;
  emptyMessage?: string;
  count?: number;
}

// 文件信息类型定义
export interface FileIconInfo {
  icon: any;
  color: string;
  type: string;
}

// 文件夹配置映射
export const FOLDER_CONFIGS: Record<string, FolderConfig> = {
  home: {
    name: "首页",
    icon: Home,
    iconColor: "text-primary",
  },
  models: {
    name: "模型",
    icon: Box,
    iconColor: "text-blue-500",
    fileType: "模型",
    emptyMessage: "开始上传您的3D模型文件",
  },
  scenes: {
    name: "场景",
    icon: Globe,
    iconColor: "text-green-500",
    fileType: "场景",
    emptyMessage: "上传您的场景文件开始构建世界",
  },
  animations: {
    name: "动画",
    icon: Play,
    iconColor: "text-purple-500",
    fileType: "动画",
    emptyMessage: "上传动画文件让角色动起来",
  },
  effects: {
    name: "特效",
    icon: Sparkles,
    iconColor: "text-yellow-500",
    fileType: "特效",
    emptyMessage: "添加特效让场景更加炫酷",
  },
  music: {
    name: "音乐",
    icon: Music,
    iconColor: "text-orange-500",
    fileType: "音频",
    emptyMessage: "上传音乐文件为项目添加声音",
  },
  blueprints: {
    name: "蓝图",
    icon: Image,
    iconColor: "text-cyan-500",
    fileType: "贴图",
    emptyMessage: "上传贴图文件为模型添加细节",
  },
  graphics: {
    name: "平面工程",
    icon: FileImage,
    iconColor: "text-pink-500",
    fileType: "设计文件",
    emptyMessage: "上传设计文件管理UI资源",
  },
  "ae-projects": {
    name: "AE工程",
    icon: Video,
    iconColor: "text-indigo-500",
    fileType: "AE项目",
    emptyMessage: "上传AE项目文件管理动画",
  },
  "ue-projects": {
    name: "UE工程",
    icon: Gamepad2,
    iconColor: "text-red-500",
    fileType: "UE项目",
    emptyMessage: "上传UE项目文件管理游戏",
  },
  artwork: {
    name: "原画",
    icon: Paintbrush,
    iconColor: "text-emerald-500",
    fileType: "原画",
    emptyMessage: "上传原画作品展示创意",
  },
  fonts: {
    name: "字体",
    icon: Type,
    iconColor: "text-slate-500",
    fileType: "字体",
    emptyMessage: "上传字体文件管理排版",
  },
  trash: {
    name: "回收站",
    icon: Trash2,
    iconColor: "text-red-600",
  },
};

// 文件扩展名到图标的映射
export const FILE_TYPE_MAPPING: Record<string, FileIconInfo> = {
  // 3D模型文件
  fbx: { icon: Box, color: "text-blue-500", type: "model" },
  obj: { icon: Box, color: "text-blue-500", type: "model" },
  blend: { icon: Box, color: "text-blue-500", type: "model" },
  ma: { icon: Box, color: "text-blue-500", type: "model" },
  mb: { icon: Box, color: "text-blue-500", type: "model" },

  // 场景文件
  unity: { icon: Globe, color: "text-green-500", type: "scene" },
  unreal: { icon: Globe, color: "text-green-500", type: "scene" },

  // 动画文件
  anim: { icon: Play, color: "text-purple-500", type: "animation" },

  // 特效文件
  vfx: { icon: Sparkles, color: "text-yellow-500", type: "effect" },
  particle: { icon: Sparkles, color: "text-yellow-500", type: "effect" },

  // 音频文件
  mp3: { icon: Music, color: "text-orange-500", type: "audio" },
  wav: { icon: Music, color: "text-orange-500", type: "audio" },
  ogg: { icon: Music, color: "text-orange-500", type: "audio" },
  flac: { icon: Music, color: "text-orange-500", type: "audio" },

  // 贴图文件
  jpg: { icon: Image, color: "text-cyan-500", type: "texture" },
  jpeg: { icon: Image, color: "text-cyan-500", type: "texture" },
  png: { icon: Image, color: "text-cyan-500", type: "texture" },
  tga: { icon: Image, color: "text-cyan-500", type: "texture" },
  exr: { icon: Image, color: "text-cyan-500", type: "texture" },

  // 设计文件
  psd: { icon: FileImage, color: "text-pink-500", type: "design" },
  ai: { icon: FileImage, color: "text-pink-500", type: "design" },
  sketch: { icon: FileImage, color: "text-pink-500", type: "design" },

  // 视频工程文件
  aep: { icon: Video, color: "text-indigo-500", type: "video" },
  prproj: { icon: Video, color: "text-indigo-500", type: "video" },

  // 游戏工程文件
  uproject: { icon: Gamepad2, color: "text-red-500", type: "project" },
  uasset: { icon: Gamepad2, color: "text-red-500", type: "project" },

  // 字体文件
  ttf: { icon: Type, color: "text-slate-500", type: "font" },
  otf: { icon: Type, color: "text-slate-500", type: "font" },
  woff: { icon: Type, color: "text-slate-500", type: "font" },
  woff2: { icon: Type, color: "text-slate-500", type: "font" },
};

/**
 * 获取文件夹配置
 */
export function useFolderConfig() {
  /**
   * 根据文件夹类型获取配置
   */
  const getFolderConfig = (folderType: string): FolderConfig => {
    return (
      FOLDER_CONFIGS[folderType] || {
        name: folderType,
        icon: Folder,
        iconColor: "text-blue-400",
        description: "普通文件夹",
      }
    );
  };

  /**
   * 根据文件名获取文件图标信息
   */
  const getFileIcon = (filename: string): FileIconInfo => {
    const ext = filename.toLowerCase().split(".").pop();
    return (
      FILE_TYPE_MAPPING[ext || ""] || {
        icon: FileIcon,
        color: "text-gray-500",
        type: "file",
      }
    );
  };

  /**
   * 获取所有文件夹配置（用于侧边栏和主页显示）
   */
  const getAllFolderConfigs = (): Array<FolderConfig & { path: string; key: string }> => {
    return Object.entries(FOLDER_CONFIGS)
      .filter(([key]) => key !== "home" && key !== "trash")
      .map(([key, config]) => ({
        ...config,
        key,
        path: `/resources/${key}`,
      }));
  };

  /**
   * 根据路径段生成面包屑项目
   */
  const generateBreadcrumbItem = (segment: string, isLast: boolean, currentPath: string) => {
    // 处理特殊路径
    if (segment === "resources") {
      return null; // 跳过，已包含在首页中
    }

    if (segment === "trash") {
      return {
        name: "回收站",
        path: isLast ? undefined : "/trash",
        icon: Trash2,
        iconColor: "text-red-600",
        type: "folder" as const,
      };
    }

    // 检查是否为已知文件夹类型
    if (FOLDER_CONFIGS[segment]) {
      const config = FOLDER_CONFIGS[segment];
      return {
        name: config.name,
        path: isLast ? undefined : currentPath,
        icon: config.icon,
        iconColor: config.iconColor,
        type: "category" as const,
      };
    }

    // 检查是否为文件
    if (segment.includes(".")) {
      const fileInfo = getFileIcon(segment);
      return {
        name: decodeURIComponent(segment),
        icon: fileInfo.icon,
        iconColor: fileInfo.color,
        type: "file" as const,
      };
    }

    // 普通文件夹
    return {
      name: decodeURIComponent(segment),
      path: isLast ? undefined : currentPath,
      icon: Folder,
      iconColor: "text-blue-400",
      type: "folder" as const,
    };
  };

  /**
   * 根据文件类型获取对应的图标组件
   */
  const getFileTypeIcon = (type: string) => {
    switch (type) {
      case "model":
        return Box;
      case "scene":
        return Globe;
      case "animation":
        return Play;
      case "effect":
        return Sparkles;
      case "audio":
        return Music;
      case "texture":
        return Image;
      case "design":
        return FileImage;
      case "video":
        return Video;
      case "project":
        return Gamepad2;
      case "artwork":
        return Paintbrush;
      case "font":
        return Type;
      default:
        return FileIcon;
    }
  };

  /**
   * 根据文件类型获取对应的颜色类名
   */
  const getFileTypeColor = (type: string) => {
    switch (type) {
      case "model":
        return "text-blue-500";
      case "scene":
        return "text-green-500";
      case "animation":
        return "text-purple-500";
      case "effect":
        return "text-yellow-500";
      case "audio":
        return "text-orange-500";
      case "texture":
        return "text-cyan-500";
      case "design":
        return "text-pink-500";
      case "video":
        return "text-indigo-500";
      case "project":
        return "text-red-500";
      case "artwork":
        return "text-emerald-500";
      case "font":
        return "text-slate-500";
      default:
        return "text-gray-500";
    }
  };

  /**
   * 根据文件类型获取对应的中文标签
   */
  const getFileTypeLabel = (type: string) => {
    switch (type) {
      case "model":
        return "3D模型";
      case "scene":
        return "场景文件";
      case "animation":
        return "动画文件";
      case "effect":
        return "特效文件";
      case "audio":
        return "音频文件";
      case "texture":
        return "贴图文件";
      case "design":
        return "设计文件";
      case "video":
        return "视频工程";
      case "project":
        return "项目文件";
      case "artwork":
        return "原画作品";
      case "font":
        return "字体文件";
      default:
        return "普通文件";
    }
  };

  return {
    getFolderConfig,
    getFileIcon,
    getAllFolderConfigs,
    generateBreadcrumbItem,
    getFileTypeIcon,
    getFileTypeColor,
    getFileTypeLabel,
    FOLDER_CONFIGS,
    FILE_TYPE_MAPPING,
  };
}
