import { ref, computed, readonly, onBeforeUnmount } from "vue";

export interface FilePreviewOptions {
  maxPreviewSize?: number; // 最大预览文件大小 (字节)
  supportedImageTypes?: string[];
  supportedVideoTypes?: string[];
  supportedTextTypes?: string[];
}

export const useFilePreview = (file: File, options: FilePreviewOptions = {}) => {
  const {
    maxPreviewSize = Number.MAX_SAFE_INTEGER, // 移除大小限制，支持所有文件预览
    supportedImageTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp", "image/svg+xml"],
    supportedVideoTypes = [
      "video/mp4",
      "video/webm",
      "video/ogg",
      "video/avi",
      "video/mov",
      "video/quicktime",
      "video/x-msvideo",
      "video/3gpp",
      "video/x-ms-wmv",
      "video/mp2t",
      "video/x-flv",
      "video/x-matroska",
      "video/mkv",
    ],
    supportedTextTypes = ["text/plain", "text/html", "text/css", "text/javascript", "application/json", "text/xml"],
  } = options;

  // 状态
  const previewUrl = ref<string>("");
  const textContent = ref<string>("");
  const isLoading = ref(false);
  const hasError = ref(false);

  // 文件类型判断
  const isImage = computed(() => {
    return supportedImageTypes.includes(file.type.toLowerCase());
  });

  const isVideo = computed(() => {
    const type = file.type.toLowerCase();
    return supportedVideoTypes.includes(type) || type.startsWith("video/");
  });

  const isPDF = computed(() => {
    return file.type.toLowerCase() === "application/pdf";
  });

  const isText = computed(() => {
    const type = file.type.toLowerCase();
    return supportedTextTypes.includes(type) || type.includes("javascript") || type.includes("typescript") || type.includes("json");
  });

  // 预览类型
  const previewType = computed<"image" | "video" | "pdf" | "text" | "none">(() => {
    if (isImage.value) return "image";
    if (isVideo.value) return "video";
    if (isPDF.value) return "pdf";
    if (isText.value) return "text";
    return "none";
  });

  // 是否可以预览
  const canPreview = computed(() => {
    return previewType.value !== "none" && file.size <= maxPreviewSize;
  });

  // 是否有预览内容
  const hasPreview = computed(() => {
    return canPreview.value && (previewUrl.value || textContent.value) && !hasError.value;
  });

  // 处理预览错误
  const handlePreviewError = () => {
    hasError.value = true;
    isLoading.value = false;
    console.warn(`文件预览失败: ${file.name}`);
  };

  // 生成预览
  const generatePreview = async () => {
    if (!canPreview.value) {
      console.log(`文件 ${file.name} 不支持预览: 类型=${file.type}, 预览类型=${previewType.value}`);
      return;
    }

    console.log(`开始生成预览: ${file.name}, 类型=${file.type}, 预览类型=${previewType.value}`);
    isLoading.value = true;
    hasError.value = false;

    try {
      if (previewType.value === "image" || previewType.value === "video" || previewType.value === "pdf") {
        // 为媒体文件和PDF创建对象URL
        previewUrl.value = URL.createObjectURL(file);
        console.log(`预览URL已创建: ${file.name}`);
      } else if (previewType.value === "text") {
        // 读取文本内容
        const text = await file.text();
        // 限制显示的文本长度以提高性能和预览效果
        const maxLength = 300; // 减少显示长度，更适合作为预览封面
        textContent.value = text.length > maxLength ? text.substring(0, maxLength) + "..." : text;
      }
    } catch (error) {
      console.error("生成预览失败:", error);
      handlePreviewError();
    } finally {
      isLoading.value = false;
    }
  };

  // 清理预览资源
  const cleanupPreview = () => {
    if (previewUrl.value) {
      URL.revokeObjectURL(previewUrl.value);
      previewUrl.value = "";
    }
    textContent.value = "";
    hasError.value = false;
    isLoading.value = false;
  };

  // 重试预览
  const retryPreview = () => {
    cleanupPreview();
    generatePreview();
  };

  // 自动生成预览
  generatePreview();

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    cleanupPreview();
  });

  return {
    // 状态
    previewUrl: readonly(previewUrl),
    textContent: readonly(textContent),
    isLoading: readonly(isLoading),
    hasError: readonly(hasError),

    // 计算属性
    previewType: readonly(previewType),
    canPreview: readonly(canPreview),
    hasPreview: readonly(hasPreview),
    isImage: readonly(isImage),
    isVideo: readonly(isVideo),
    isPDF: readonly(isPDF),
    isText: readonly(isText),

    // 方法
    generatePreview,
    cleanupPreview,
    retryPreview,
    handlePreviewError,
  };
};

// 文件图标映射
export const getFileIconType = (fileType: string): string => {
  const type = fileType.toLowerCase();

  if (type.startsWith("image/")) return "image";
  if (type.startsWith("video/")) return "video";
  if (type.startsWith("audio/")) return "audio";
  if (type.startsWith("text/") || type.includes("json") || type.includes("javascript") || type.includes("typescript")) return "text";
  if (type.includes("zip") || type.includes("rar") || type.includes("7z")) return "archive";
  if (type === "application/pdf") return "pdf";
  if (type.includes("word") || type.includes("document")) return "document";
  if (type.includes("excel") || type.includes("sheet")) return "spreadsheet";
  if (type.includes("powerpoint") || type.includes("presentation")) return "presentation";

  return "file";
};
