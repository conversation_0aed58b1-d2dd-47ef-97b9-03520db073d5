import { useTusUpload, type UploadCallbacks } from "@/components/Upload/composables/useTusUpload";

/**
 * 高级上传策略 Composable
 * 提供简化的接口和自动错误处理
 *
 * 使用场景：
 * - 需要简单上传功能，不想处理复杂的回调逻辑
 * - 需要统一的错误处理，由全局任务管理器负责用户提示
 * - 需要便捷的拖拽、文件输入等辅助方法
 */
export function useUploadStrategy() {
  const tusUpload = useTusUpload();

  /**
   * 智能上传文件 - 统一入口
   * 自动判断并选择最佳上传方式，回调处理由全局任务管理器统一管理
   */
  const uploadFiles = async (
    files: File[],
    attributes?: Record<string, string>,
    options?: {
      onFileUploaded?: UploadCallbacks["onFileUploaded"];
      onAllFilesUploaded?: UploadCallbacks["onAllFilesUploaded"];
      onUploadError?: UploadCallbacks["onUploadError"];
    }
  ): Promise<void> => {
    if (!files || files.length === 0) {
      console.warn("没有选择文件");
      return;
    }

    try {
      console.log(`开始智能上传 ${files.length} 个文件`);

      // 使用增强的元数据
      const metadata = {
        ...attributes,
        uploadTimestamp: new Date().toISOString(),
        uploadSource: "strategy-manager", // 标识来源
      };

      // 创建增强的回调
      const enhancedCallbacks: UploadCallbacks = {
        onFileUploaded: (file, task) => {
          options?.onFileUploaded?.(file, task);
        },
        onAllFilesUploaded: (files, tasks) => {
          options?.onAllFilesUploaded?.(files, tasks);
        },
        onUploadError: (error, failedTasks) => {
          options?.onUploadError?.(error, failedTasks);
        },
      };

      // 调用 TUS 的智能上传方法，传递增强的回调
      await tusUpload.uploadFiles(files, metadata, enhancedCallbacks);

      console.log(`上传任务已启动: ${files.length} 个文件`);
    } catch (error) {
      console.error("文件上传失败:", error);
      throw error;
    }
  };

  /**
   * 通过文件选择对话框上传
   */
  const uploadFromDialog = async (): Promise<void> => {
    try {
      console.log("通过文件选择对话框上传");

      // 直接调用上传
      await tusUpload.uploadFromDialog();
    } catch (error) {
      console.error("文件选择上传失败:", error);
      throw error;
    }
  };

  /**
   * 处理拖拽上传 - 简化接口
   */
  const handleDrop = async (files: File[], attributes?: Record<string, string>, options?: Parameters<typeof uploadFiles>[2]): Promise<void> => {
    return uploadFiles(files, { ...attributes, uploadSource: "drag-drop" }, options);
  };

  /**
   * 处理文件输入上传 - 简化接口
   */
  const handleFileInput = async (files: File[], attributes?: Record<string, string>, options?: Parameters<typeof uploadFiles>[2]): Promise<void> => {
    return uploadFiles(files, { ...attributes, uploadSource: "file-input" }, options);
  };

  /**
   * 创建预设的上传选项
   */
  const createUploadOptions = (overrides?: Partial<Parameters<typeof uploadFiles>[2]>) => {
    return {
      ...overrides,
    };
  };

  /**
   * 静默上传（无提示）
   */
  const uploadFilesSilently = async (
    files: File[],
    attributes?: Record<string, string>,
    callbacks?: Pick<UploadCallbacks, "onFileUploaded" | "onAllFilesUploaded" | "onUploadError">
  ): Promise<void> => {
    return uploadFiles(files, attributes, {
      ...callbacks,
    });
  };

  return {
    // 主要方法
    uploadFiles,
    uploadFromDialog,
    uploadFilesSilently,

    // 便捷方法
    handleDrop,
    handleFileInput,

    // 工具方法
    createUploadOptions,

    // 底层访问（高级用法）
    tusUpload,
  };
}

// 导出上传选项类型
export type UploadStrategyOptions = Parameters<ReturnType<typeof useUploadStrategy>["uploadFiles"]>[2];

// 导出便捷类型
export type { UploadCallbacks };
