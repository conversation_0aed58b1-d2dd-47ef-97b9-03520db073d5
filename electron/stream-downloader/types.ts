// StreamSaver 下载模块类型定义

export type DownloadStatus = "pending" | "downloading" | "paused" | "completed" | "error" | "cancelled" | "extracting" | "extract-completed" | "extract-error";

// 解压缩状态类型
export type ExtractionStatus = "pending" | "extracting" | "paused" | "completed" | "error" | "cancelled";

// 下载任务接口
export interface DownloadTask {
  id: string;
  url: string;
  fileName: string;
  filePath: string; // 保存路径
  fileSize?: number;
  progress: number;
  status: DownloadStatus;
  bytesDownloaded: number;
  downloadSpeed: number;
  remainingTime: number;
  startTime: Date;
  endTime?: Date;
  error?: string;
  metadata?: Record<string, any>;
  resumable: boolean;
  ranges?: DownloadRange[]; // 分片信息
  chunkSize: number;
  totalChunks: number;
  completedChunks: number;
  headers?: Record<string, string>;
  retryCount?: number; // 重试次数
  isSubTask?: boolean; // 是否为子任务
  batchId?: string; // 批次ID

  // 解压缩相关字段
  needsExtraction?: boolean; // 是否需要解压缩（7z文件）
  extractionTaskId?: string; // 关联的解压缩任务ID
  extractionStatus?: ExtractionStatus; // 解压缩状态
  extractionProgress?: number; // 解压缩进度 (0-100)
  extractionError?: string; // 解压缩错误信息
  extractPath?: string; // 解压目标路径
  deleteAfterExtraction?: boolean; // 解压完成后是否删除原文件
}

// 下载分片信息
export interface DownloadRange {
  start: number;
  end: number;
  completed: boolean;
  chunkIndex: number;
}

// 下载配置接口
export interface StreamDownloadConfig {
  chunkSize?: number; // 分片大小，默认 5MB
  maxConcurrent?: number; // 最大并发下载数，默认 10
  retryDelays?: number[]; // 重试延迟，默认 [0, 1000, 3000, 5000]
  maxRetries?: number; // 最大重试次数，默认 3
  timeout?: number; // 超时时间，默认 30s
  headers?: Record<string, string>; // 默认请求头
  downloadDir?: string; // 默认下载目录
}

// 下载事件接口
export interface DownloadEvents {
  "task-created": (taskId: string, task: DownloadTask) => void;
  "task-progress": (taskId: string, progress: number, bytesDownloaded: number, bytesTotal: number) => void;
  "task-status-changed": (taskId: string, status: DownloadStatus, error?: string) => void;
  "task-completed": (taskId: string) => void;
  "task-error": (taskId: string, error: string) => void;
}

// API 响应接口
export interface DownloadApiResponse<T = any> {
  success: boolean;
  error?: string;
  taskId?: string;
  task?: DownloadTask;
  tasks?: DownloadTask[];
  data?: T;
}

// 存储数据接口
export interface DownloadStoreData {
  tasks: Record<string, DownloadTask>;
  settings: {
    chunkSize: number;
    maxConcurrent: number;
    retryDelays: number[];
    maxRetries: number;
    timeout: number;
    downloadDir: string;
  };
}

// 批量下载任务接口
export interface BatchDownloadTask {
  id: string;
  batchName: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  totalSize: number;
  progress: number;
  status: DownloadStatus;
  startTime: Date;
  endTime?: Date;
  duration: number;
  error?: string;
  subTasks: string[]; // 子任务ID列表
  expanded?: boolean; // 展开状态
}

// 下载历史任务接口
export interface DownloadHistoryTask {
  id: string;
  type: "download";
  fileName: string;
  fileSize: number;
  filePath: string;
  url: string;
  status: "completed" | "error" | "cancelled";
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: string;
}

// 批量下载历史任务接口
export interface BatchDownloadHistoryTask {
  id: string;
  type: "batch";
  batchName: string;
  totalFiles: number;
  completedFiles: number;
  failedFiles: number;
  totalSize: number;
  status: "completed" | "error" | "cancelled";
  startTime: Date;
  endTime: Date;
  duration: number;
  error?: string;
  subTasks: Array<{
    id: string;
    fileName: string;
    fileSize?: number;
    status: "completed" | "error" | "cancelled";
    error?: string;
  }>;
}
