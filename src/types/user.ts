/**
 * 用户信息相关的TypeScript类型定义
 */

// 用户基本信息
export interface UserInfo {
  user_id: string;
  username: string;
  expire_at: string;
}

// 菜单项
export interface MenuItem {
  code: string;
  name: string;
  url: string;
}

// getUserInfo API响应数据结构
export interface GetUserInfoData {
  token: string;
  user_info: UserInfo;
  menu: MenuItem[];
}

// getUserInfo API完整响应结构
export interface GetUserInfoResponse {
  code: number;
  msg: string;
  data: GetUserInfoData;
}

// 用户状态管理相关类型
export interface UserState {
  userInfo: UserInfo | null;
  menu: MenuItem[];
  isLoaded: boolean;
  isLoading: boolean;
  error: string | null;
}

// 菜单代码枚举（用于类型安全）
export enum MenuCode {
  RESOURCES = 'resources',
  TAGS = 'tags', 
  PERMISSIONS = 'permissions'
}

// 导航项类型（扩展MenuItem用于前端导航）
export interface NavigationItem extends MenuItem {
  current: boolean;
  icon: any;
  href: string;
}
