<template>
  <div class="relative overflow-hidden rounded-lg" :class="containerClass">
    <!-- 图片预览 -->
    <img v-if="previewType === 'image' && previewUrl" :src="previewUrl" :alt="fileName"
      class="object-contain w-full h-full" @error="handlePreviewError" />

    <!-- 视频预览 -->
    <video v-else-if="previewType === 'video' && previewUrl" :src="previewUrl" class="object-cover w-full h-full" muted
      preload="metadata" playsinline @error="handlePreviewError" @loadeddata="() => { }">
      您的浏览器不支持视频预览
    </video>

    <!-- PDF 预览（显示第一页） -->
    <iframe v-else-if="previewType === 'pdf' && previewUrl" :src="previewUrl" class="w-full h-full border-0"
      @error="handlePreviewError"></iframe>

    <!-- 文本文件预览 -->
    <div v-else-if="previewType === 'text' && textContent"
      class="w-full h-full p-2 overflow-hidden text-[10px] bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-900 border border-slate-200/50 dark:border-slate-700/50">
      <pre
        class="text-slate-600 break-words whitespace-pre-wrap leading-tight dark:text-slate-300 font-mono">{{ textContent }}</pre>
    </div>

    <!-- 默认图标显示 -->
    <div v-else
      class="flex flex-col items-center justify-center w-full h-full bg-gradient-to-br from-muted to-muted/70 relative">
      <!-- 图标背景圆圈 -->
      <div
        class="flex items-center justify-center w-8 h-8 mb-1 rounded-full bg-background/80 shadow-sm border border-border/50">
        <component :is="getFileIcon(fileType)" :class="getIconSizeClass(iconClass)" :style="getIconColor(fileType)" />
      </div>
      <!-- 文件类型标签 -->
      <div class="text-[8px] text-muted-foreground/80 font-medium truncate max-w-full px-1">
        {{ getFileTypeLabel(fileType) }}
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-muted/50 backdrop-blur-sm">
      <div class="w-4 h-4 border-2 rounded-full border-primary border-t-transparent animate-spin"></div>
    </div>

    <!-- 错误状态 -->
    <div v-if="hasError"
      class="absolute inset-0 flex flex-col items-center justify-center bg-destructive/10 backdrop-blur-sm">
      <div
        class="flex items-center justify-center w-8 h-8 mb-1 rounded-full bg-destructive/20 border border-destructive/30">
        <component :is="getFileIcon(fileType)" class="w-4 h-4 text-destructive/70" />
      </div>
      <div class="text-[8px] text-destructive/60 font-medium">
        预览失败
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { File, Image, Video, Music, Archive, FileText } from 'lucide-vue-next'
import { useFilePreview, getFileIconType } from './composables/useFilePreview'

// Props
const props = withDefaults(defineProps<{
  file: File
  fileName: string
  fileType: string
  size?: 'sm' | 'md' | 'lg'
  containerClass?: string
  iconClass?: string
  maxPreviewSize?: number // 最大预览文件大小 (字节)，默认无限制
}>(), {
  size: 'md',
  containerClass: '',
  iconClass: 'w-6 h-6',
  maxPreviewSize: Number.MAX_SAFE_INTEGER // 移除大小限制
})

// 使用文件预览 composable
const {
  previewUrl,
  textContent,
  isLoading,
  hasError,
  previewType,
  handlePreviewError
} = useFilePreview(props.file, {
  maxPreviewSize: props.maxPreviewSize
})

// 获取文件图标组件
const getFileIcon = (fileType: string) => {
  const iconType = getFileIconType(fileType)

  switch (iconType) {
    case 'image': return Image
    case 'video': return Video
    case 'audio': return Music
    case 'text': return FileText
    case 'archive': return Archive
    case 'pdf': return FileText
    case 'document': return FileText
    case 'spreadsheet': return FileText
    case 'presentation': return FileText
    default: return File
  }
}

// 获取图标尺寸类
const getIconSizeClass = (_defaultClass: string) => {
  // 对于网格视图，使用固定的较小尺寸
  return 'w-4 h-4'
}

// 获取图标颜色
const getIconColor = (fileType: string) => {
  const iconType = getFileIconType(fileType)

  const colorMap = {
    image: 'color: #22c55e', // green-500
    video: 'color: #3b82f6', // blue-500  
    audio: 'color: #a855f7', // purple-500
    text: 'color: #64748b',   // slate-500
    archive: 'color: #f59e0b', // amber-500
    pdf: 'color: #dc2626',    // red-600
    document: 'color: #2563eb', // blue-600
    spreadsheet: 'color: #16a34a', // green-600
    presentation: 'color: #ea580c', // orange-600
    file: 'color: #64748b'    // slate-500
  }

  return colorMap[iconType as keyof typeof colorMap] || colorMap.file
}

// 获取文件类型标签
const getFileTypeLabel = (fileType: string) => {
  const iconType = getFileIconType(fileType)

  const labelMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    text: '文本',
    archive: '压缩包',
    pdf: 'PDF',
    document: '文档',
    spreadsheet: '表格',
    presentation: '演示',
    file: '文件'
  }

  return labelMap[iconType as keyof typeof labelMap] || '文件'
}
</script>