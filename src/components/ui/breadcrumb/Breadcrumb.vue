<script lang="ts" setup>
import type { HTMLAttributes } from 'vue'
import { ChevronRight } from 'lucide-vue-next'
import { cn } from '@/lib/utils'

interface BreadcrumbItem {
  name: string
  path: string
  icon?: any
  iconColor?: string
}

const props = defineProps<{
  class?: HTMLAttributes['class'],
  breadcrumbs: BreadcrumbItem[]
}>()
</script>

<template>
  <nav :class="cn('flex items-center space-x-1 text-sm text-muted-foreground', props.class)">
    <template v-for="(item, index) in props.breadcrumbs" :key="index">
      <router-link v-if="index < props.breadcrumbs.length - 1" :to="item.path"
        class="flex items-center gap-1 transition-colors hover:text-foreground">
        <component v-if="item.icon" :is="item.icon" class="w-4 h-4" :class="item.iconColor" />
        {{ item.name }}
      </router-link>
      <span v-else class="flex items-center gap-1 font-medium text-foreground">
        <component v-if="item.icon" :is="item.icon" class="w-4 h-4" :class="item.iconColor" />
        {{ item.name }}
      </span>
      <ChevronRight v-if="index < props.breadcrumbs.length - 1" class="w-4 h-4 text-muted-foreground/50" />
    </template>
  </nav>
</template>
