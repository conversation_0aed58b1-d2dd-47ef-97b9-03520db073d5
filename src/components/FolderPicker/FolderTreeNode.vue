<template>
  <div class="folder-tree-node">
    <!-- Current folder item -->
    <div 
      class="flex items-center py-2 px-2 rounded-md cursor-pointer transition-colors hover:bg-accent/50" 
      :class="{
        'bg-accent border border-primary/50': isSelected,
        'hover:bg-accent': !isSelected,
        'opacity-50 cursor-not-allowed': isDisabled
      }" 
      :style="{ paddingLeft: `${level * 20 + 8}px` }" 
      @click="handleSelect"
    >
      <!-- Expand/Collapse Icon -->
      <div class="w-4 h-4 mr-2 flex items-center justify-center">
        <button 
          v-if="showExpandButton"
          class="w-4 h-4 flex items-center justify-center hover:bg-accent rounded-sm transition-colors"
          @click.stop="handleToggleExpand" 
          :disabled="node.isLoading" 
          :title="node.isExpanded ? '折叠' : '展开'"
        >
          <Loader2 v-if="node.isLoading" class="w-3 h-3 animate-spin" />
          <ChevronDown v-else-if="node.isExpanded" class="w-3 h-3" />
          <ChevronRight v-else class="w-3 h-3" />
        </button>
        <div v-else class="w-4 h-4"></div>
      </div>

      <!-- Folder Icon -->
      <Folder class="w-4 h-4 mr-2 text-blue-500 flex-shrink-0" />

      <!-- Folder Name -->
      <span class="text-sm font-medium truncate flex-1">{{ node.name }}</span>
    </div>

    <!-- Children (recursive) -->
    <div v-if="node.isExpanded && node.children.length > 0" class="children">
      <FolderTreeNode 
        v-for="child in node.children" 
        :key="child.id" 
        :node="child" 
        :level="level + 1"
        :selected-folder-id="selectedFolderId" 
        :current-parent-id="currentParentId" 
        @select="$emit('select', $event)"
        @toggle-expand="$emit('toggle-expand', $event)" 
      />
    </div>

    <!-- Loading placeholder for children -->
    <div 
      v-if="node.isExpanded && node.isLoading && node.children.length === 0"
      class="flex items-center py-2 text-muted-foreground text-sm"
      :style="{ paddingLeft: `${(level + 1) * 20 + 8}px` }"
    >
      <Loader2 class="w-3 h-3 animate-spin mr-2" />
      加载中...
    </div>

    <!-- Empty state for expanded folders with no children -->
    <div 
      v-if="node.isExpanded && !node.isLoading && node.children.length === 0 && node.hasChildren"
      class="flex items-center py-2 text-muted-foreground text-sm"
      :style="{ paddingLeft: `${(level + 1) * 20 + 8}px` }"
    >
      <Folder class="w-3 h-3 mr-2 opacity-50" />
      此文件夹为空
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Folder, ChevronRight, ChevronDown, Loader2 } from 'lucide-vue-next'

/**
 * 树节点接口定义
 */
export interface TreeNode {
  id: string
  name: string
  hasChildren: boolean
  isExpanded: boolean
  isLoading: boolean
  children: TreeNode[]
  itemCount?: number
  parentId?: string
}

// Props
const props = defineProps<{
  /** 树节点数据 */
  node: TreeNode
  /** 当前层级（用于缩进计算） */
  level: number
  /** 当前选中的文件夹ID */
  selectedFolderId: string
  /** 当前父级文件夹ID（用于禁用选择） */
  currentParentId?: string
}>()

// Emits
const emit = defineEmits<{
  /** 选择节点事件 */
  select: [nodeId: string]
  /** 切换展开/折叠事件 */
  'toggle-expand': [nodeId: string]
}>()

// Computed properties
const isSelected = computed(() => props.selectedFolderId === props.node.id)

const isCurrentParent = computed(() => props.currentParentId === props.node.id)

const isDisabled = computed(() => isCurrentParent.value)

// 显示展开按钮的条件：有子文件夹或者已经有加载的子节点
const showExpandButton = computed(() => {
  return props.node.hasChildren || props.node.children.length > 0
})

// Event handlers
const handleSelect = () => {
  if (!isDisabled.value) {
    emit('select', props.node.id)
  }
}

const handleToggleExpand = () => {
  if (showExpandButton.value && !props.node.isLoading) {
    emit('toggle-expand', props.node.id)
  }
}
</script>

<style scoped>
.folder-tree-node {
  @apply select-none;
}

.children {
  @apply border-l border-border/30 ml-2;
}

/* Hover effects */
.folder-tree-node:hover .children {
  @apply border-border/50;
}
</style>
