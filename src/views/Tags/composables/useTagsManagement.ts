import { ref, computed, reactive } from "vue";
import { toast } from "vue-sonner";
import tagsService, { type TagCategory, type EditTagParams } from "@/api/services/tags";

export interface TagsManagementState {
  categories: TagCategory[];
  loading: boolean;
  error: string | null;
  searchQuery: string;
  selectedIds: Set<string>;
  editingCategory: TagCategory | null;
}

export function useTagsManagement() {
  // 响应式状态
  const state = reactive<TagsManagementState>({
    categories: [],
    loading: false,
    error: null,
    searchQuery: "",
    selectedIds: new Set(),
    editingCategory: null,
  });

  // 编辑状态
  const isEditDialogOpen = ref(false);

  // 计算属性
  const hasSelection = computed(() => state.selectedIds.size > 0);
  const selectedCount = computed(() => state.selectedIds.size);

  // 搜索状态
  const isSearching = computed(() => Boolean(state.searchQuery && state.searchQuery.trim()));
  const hasSearchResults = computed(() => isSearching.value && filteredCategories.value.length > 0);
  const isEmptySearchResult = computed(() => isSearching.value && filteredCategories.value.length === 0);

  // 过滤后的类别列表（用于搜索）
  const filteredCategories = computed(() => {
    // 如果没有搜索查询，返回所有类别
    if (!state.searchQuery || !state.searchQuery.trim()) {
      return state.categories;
    }

    const query = state.searchQuery.toLowerCase().trim();

    // 如果没有类别数据，直接返回空数组
    if (!state.categories || state.categories.length === 0) {
      return [];
    }

    // 过滤匹配的类别，搜索不到时自动返回空数组
    const filtered = state.categories.filter((category) => {
      // 确保类别对象存在且有名称
      if (!category || !category.category_name) {
        return false;
      }

      // 搜索类别名称
      const categoryNameMatch = category.category_name.toLowerCase().includes(query);

      // 搜索标签内容
      const tagMatch =
        category.tags && Array.isArray(category.tags) && category.tags.length > 0 ? category.tags.some((tag) => tag && tag.item_value && tag.item_value.toLowerCase().includes(query)) : false;

      return categoryNameMatch || tagMatch;
    });

    return filtered; // 搜索不到时返回空数组 []
  });

  // 获取标签列表
  const fetchTags = async () => {
    try {
      state.loading = true;
      state.error = null;

      const response = await tagsService.tags.getTagList();

      if (response.code === 0 && response.data) {
        state.categories = response.data;
      } else {
        throw new Error(response.msg || "获取标签列表失败");
      }
    } catch (error) {
      state.error = error instanceof Error ? error.message : "获取标签列表失败";
      toast.error(state.error);
    } finally {
      state.loading = false;
    }
  };

  // 编辑标签（更新指定类别的标签列表）
  const editTag = async (data: EditTagParams) => {
    try {
      state.loading = true;
      const response = await tagsService.tags.editTag(data);

      if (response.code === 0) {
        toast.success("标签编辑成功");
        isEditDialogOpen.value = false;
        state.editingCategory = null;
        await fetchTags(); // 刷新列表
      } else {
        throw new Error(response.msg || "编辑标签失败");
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : "编辑标签失败";
      toast.error(message);
      throw error;
    } finally {
      state.loading = false;
    }
  };

  // 搜索
  const search = (query: string) => {
    try {
      // 清理搜索查询，移除前后空格，处理 null/undefined
      const cleanQuery = query ? String(query).trim() : "";

      // 如果查询没有变化，不需要重新搜索
      if (state.searchQuery === cleanQuery) {
        return;
      }

      state.searchQuery = cleanQuery;

      // 清除选择状态，因为搜索结果可能不包含之前选中的项
      state.selectedIds.clear();
    } catch (error) {
      console.warn("搜索时发生错误:", error);
      // 发生错误时重置搜索状态
      state.searchQuery = "";
      state.selectedIds.clear();
    }
  };

  // 清除搜索
  const clearSearch = () => {
    state.searchQuery = "";
    state.selectedIds.clear();
  };

  // 编辑操作
  const openEditDialog = (category: TagCategory) => {
    state.editingCategory = { ...category };
    isEditDialogOpen.value = true;
  };

  const closeEditDialog = () => {
    state.editingCategory = null;
    isEditDialogOpen.value = false;
  };

  // 刷新数据
  const refresh = () => {
    fetchTags();
  };

  return {
    // 状态
    state,
    isEditDialogOpen,

    // 计算属性
    hasSelection,
    selectedCount,
    filteredCategories,
    isSearching,
    hasSearchResults,
    isEmptySearchResult,

    // 方法
    fetchTags,
    editTag,
    search,
    clearSearch,
    openEditDialog,
    closeEditDialog,
    refresh,
  };
}
