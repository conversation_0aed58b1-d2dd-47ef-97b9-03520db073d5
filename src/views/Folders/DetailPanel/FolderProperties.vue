<template>
  <div class="p-4 space-y-3 border-b">
    <h5 class="text-sm font-medium text-muted-foreground">文件夹属性</h5>

    <div class="space-y-2">
      <!-- 权限 -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">权限</span>
        <span class="text-sm font-medium">{{ item.permissions || '读写' }}</span>
      </div>

      <!-- 总大小（如果有的话） -->
      <div v-if="item.totalSize" class="flex justify-between">
        <span class="text-sm text-muted-foreground">总大小</span>
        <span class="text-sm font-medium">{{ item.totalSize }}</span>
      </div>

      <!-- 子文件夹数量 -->
      <div v-if="item.subFolderCount !== undefined" class="flex justify-between">
        <span class="text-sm text-muted-foreground">子文件夹</span>
        <span class="text-sm font-medium">{{ item.subFolderCount }} 个</span>
      </div>

      <!-- 子文件数量 -->
      <div v-if="item.fileCount !== undefined" class="flex justify-between">
        <span class="text-sm text-muted-foreground">文件</span>
        <span class="text-sm font-medium">{{ item.fileCount }} 个</span>
      </div>

      <!-- 是否共享 -->
      <div v-if="item.isShared !== undefined" class="flex justify-between">
        <span class="text-sm text-muted-foreground">共享状态</span>
        <div class="flex items-center">
          <span class="mr-2 text-sm font-medium">
            {{ item.isShared ? '已共享' : '未共享' }}
          </span>
          <div class="w-2 h-2 rounded-full" :class="item.isShared ? 'bg-green-500' : 'bg-gray-400'">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { FolderItemType } from '@/types/files'

// Props
defineProps<{
  item: FolderItemType
}>()
</script>