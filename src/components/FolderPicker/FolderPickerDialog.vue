<template>
  <Dialog :open="open" @update:open="handleOpenChange">
    <DialogContent class="max-w-3xl max-h-[85vh] flex flex-col">
      <DialogHeader>
        <DialogTitle>选择目标文件夹</DialogTitle>
        <DialogDescription>
          选择要移动到的目标文件夹。展开文件夹查看子文件夹，单击选择目标位置。
        </DialogDescription>
      </DialogHeader>

      <div class="flex flex-col flex-1 min-h-0">
        <!-- 分类信息头部 -->
        <div class="flex gap-2 items-center p-3 border-b bg-muted/30">
          <component :is="categoryIcon" class="w-4 h-4 text-primary" />
          <span class="font-medium">{{ categoryName }}</span>
          <span class="text-sm text-muted-foreground">- 选择目标文件夹</span>
        </div>

        <!-- 文件夹树容器 -->
        <div class="overflow-y-auto flex-1 p-2">
          <!-- 加载状态 -->
          <div v-if="initialLoading" class="flex justify-center items-center py-12">
            <div class="flex gap-2 items-center text-muted-foreground">
              <Loader2 class="w-4 h-4 animate-spin" />
              加载文件夹结构...
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="treeData.length === 0" class="flex justify-center items-center py-12">
            <div class="text-center text-muted-foreground">
              <Folder class="mx-auto mb-2 w-8 h-8 opacity-50" />
              <div>此分类中没有文件夹</div>
            </div>
          </div>

          <!-- 文件夹树 -->
          <div v-else class="space-y-1">
            <!-- 根目录选项 -->
            <div class="flex items-center px-2 py-2 rounded-md transition-colors cursor-pointer hover:bg-accent/50"
              :class="{
                'bg-accent border border-primary/50': isRootSelected,
                'hover:bg-accent': !isRootSelected
              }" @click="selectFolder(rootParentId.toString())">
              <div class="mr-2 w-4 h-4"></div>
              <component :is="categoryIcon" class="mr-2 w-4 h-4 text-primary" />
              <span class="text-sm font-medium">{{ categoryName }} (根目录)</span>
            </div>

            <!-- 树节点 -->
            <FolderTreeNode v-for="node in treeData" :key="node.id" :node="node" :level="0"
              :selected-folder-id="selectedFolderId" :current-parent-id="currentParentId" @select="selectFolder"
              @toggle-expand="toggleNodeExpansion" />
          </div>
        </div>

        <!-- 选择信息 -->
        <div v-if="selectedFolderId" class="p-3 border-t bg-muted/30">
          <div class="text-sm">
            <span class="text-muted-foreground">选择的目标文件夹：</span>
            <span class="font-medium">{{ selectedFolderName }}</span>
          </div>
        </div>
      </div>

      <DialogFooter class="gap-2">
        <Button variant="outline" @click="handleCancel">
          取消
        </Button>
        <Button :disabled="!selectedFolderId || selectedFolderId === currentParentId" @click="handleConfirm">
          确认移动
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Folder, Loader2 } from 'lucide-vue-next'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useDirectoryStructure } from '@/composables/useDirectoryStructure'
import { useSidebarStore } from '@/store/sidebar'
import FolderTreeNode, { type TreeNode } from './FolderTreeNode.vue'
import type { FolderItemType } from '@/types/files'

/**
 * 文件夹选择器对话框组件
 * 提供树形结构的文件夹选择界面，支持懒加载和递归展开
 */

// Props
const props = defineProps<{
  /** 对话框是否打开 */
  open: boolean
  /** 分类ID */
  categoryId: string
  /** 当前父级文件夹ID（用于禁用选择） */
  currentParentId?: string
}>()

// Emits
const emit = defineEmits<{
  /** 对话框打开状态变化 */
  'update:open': [value: boolean]
  /** 确认选择文件夹 */
  confirm: [targetDirId: string]
  /** 取消选择 */
  cancel: []
}>()

// 状态管理
const initialLoading = ref(false)
const selectedFolderId = ref<string>('')
const treeData = ref<TreeNode[]>([])
const nodeCache = ref<Map<string, TreeNode[]>>(new Map())

// Composables
const { getDirectoryContents, getCategoryInfoById } = useDirectoryStructure()
const sidebarStore = useSidebarStore()

// 分类信息
const categoryInfo = computed(() => getCategoryInfoById(props.categoryId))
const categoryName = computed(() => categoryInfo.value?.name || '未知分类')
const categoryIcon = computed(() => categoryInfo.value?.icon || Folder)

// 根目录 parentId
const rootParentId = computed(() => {
  const currentCategoryId = Number(props.categoryId)
  if (currentCategoryId) {
    const categoryInfo = sidebarStore.sidebarItems.find(item => item.category_id === currentCategoryId)
    if (categoryInfo) {
      return categoryInfo.id
    }
  }
  return currentCategoryId || 1
})

// 是否选择了根目录
const isRootSelected = computed(() => selectedFolderId.value === rootParentId.value.toString())

// 选中文件夹名称
const selectedFolderName = computed(() => {
  if (!selectedFolderId.value) return ''

  if (isRootSelected.value) {
    return `${categoryName.value} (根目录)`
  }

  // 在树中查找节点名称
  const findNodeName = (nodes: TreeNode[], nodeId: string): string => {
    for (const node of nodes) {
      if (node.id === nodeId) return node.name
      if (node.children.length > 0) {
        const found = findNodeName(node.children, nodeId)
        if (found) return found
      }
    }
    return ''
  }

  return findNodeName(treeData.value, selectedFolderId.value) || '未知文件夹'
})

/**
 * 转换文件夹数据为树节点
 */
const convertToTreeNode = (folder: FolderItemType): TreeNode => ({
  id: folder.id,
  name: folder.name,
  hasChildren: true, // 默认假设有子文件夹，懒加载时检查
  isExpanded: false,
  isLoading: false,
  children: [],
  itemCount: folder.itemCount,
  parentId: folder.id
})

/**
 * 确保 sidebar 数据已加载
 */
const ensureSidebarLoaded = async () => {
  if (!sidebarStore.isLoaded && !sidebarStore.isLoading) {
    await sidebarStore.fetchSidebarData()
  } else if (sidebarStore.isLoading) {
    // 等待加载完成
    while (sidebarStore.isLoading) {
      await new Promise(resolve => setTimeout(resolve, 50))
    }
  }
}

/**
 * 加载根级文件夹
 */
const loadRootFolders = async () => {
  if (!props.categoryId) return

  initialLoading.value = true
  try {
    await ensureSidebarLoaded()

    const result = await getDirectoryContents(props.categoryId, rootParentId.value, 1, 1000)
    const folders = result.items.filter(item => item.type === 'folder') as FolderItemType[]

    if (folders.length === 0) {
      treeData.value = []
      return
    }

    const nodes = folders.map(convertToTreeNode)
    treeData.value = nodes
    nodeCache.value.set(rootParentId.value.toString(), nodes)
  } catch (error) {
    console.error('加载根文件夹失败:', error)
    treeData.value = []
  } finally {
    initialLoading.value = false
  }
}

/**
 * 加载文件夹的子文件夹
 */
const loadFolderChildren = async (nodeId: string): Promise<TreeNode[]> => {
  // 检查缓存
  if (nodeCache.value.has(nodeId)) {
    return nodeCache.value.get(nodeId)!
  }

  try {
    const result = await getDirectoryContents(props.categoryId, nodeId, 1, 1000)
    const folders = result.items.filter(item => item.type === 'folder') as FolderItemType[]

    const children = folders.map(convertToTreeNode)
    nodeCache.value.set(nodeId, children)
    return children
  } catch (error) {
    console.error('加载子文件夹失败:', { nodeId, error })
    return []
  }
}

/**
 * 在树中查找节点
 */
const findNodeInTree = (nodes: TreeNode[], nodeId: string): TreeNode | null => {
  for (const node of nodes) {
    if (node.id === nodeId) return node
    if (node.children.length > 0) {
      const found = findNodeInTree(node.children, nodeId)
      if (found) return found
    }
  }
  return null
}

/**
 * 切换节点展开/折叠
 */
const toggleNodeExpansion = async (nodeId: string) => {
  const node = findNodeInTree(treeData.value, nodeId)
  if (!node) return

  if (node.isExpanded) {
    // 折叠节点
    node.isExpanded = false
  } else {
    // 展开节点
    if (node.children.length === 0) {
      node.isLoading = true
      try {
        const children = await loadFolderChildren(nodeId)
        node.children = children

        // 如果没有子文件夹，隐藏展开按钮
        if (children.length === 0) {
          node.hasChildren = false
        }
      } catch (error) {
        console.error('加载子文件夹失败:', error)
        node.hasChildren = false
      } finally {
        node.isLoading = false
      }
    }
    node.isExpanded = true
  }
}

/**
 * 选择文件夹
 */
const selectFolder = (folderId: string) => {
  selectedFolderId.value = folderId
}

/**
 * 重置状态
 */
const resetState = () => {
  selectedFolderId.value = ''
  treeData.value = []
  // 保留缓存以提高性能
}

// 事件处理
const handleOpenChange = (value: boolean) => emit('update:open', value)
const handleConfirm = () => {
  if (selectedFolderId.value && selectedFolderId.value !== props.currentParentId) {
    emit('confirm', selectedFolderId.value)
  }
}
const handleCancel = () => emit('cancel')

// 监听对话框打开状态
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    resetState()
    loadRootFolders()
  }
})

// 初始化
onMounted(() => {
  if (props.open) {
    loadRootFolders()
  }
})
</script>
