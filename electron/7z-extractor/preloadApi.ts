import { ipc<PERSON><PERSON><PERSON> } from "electron";
import type { ExtractionTask, ExtractionApiResponse } from "./types";

// 解压缩 API 接口
export interface ExtractionPreloadApi {
  // 创建解压缩任务
  createTask: (
    archivePath: string, 
    extractPath?: string, 
    options?: {
      downloadTaskId?: string;
      deleteAfterExtraction?: boolean;
      password?: string;
    }
  ) => Promise<ExtractionApiResponse>;

  // 通过对话框选择解压路径创建任务
  createTaskWithDialog: (
    archivePath: string, 
    options?: {
      downloadTaskId?: string;
      deleteAfterExtraction?: boolean;
      password?: string;
    }
  ) => Promise<ExtractionApiResponse>;

  // 开始解压缩
  startExtraction: (taskId: string) => Promise<ExtractionApiResponse>;

  // 暂停解压缩
  pauseExtraction: (taskId: string) => Promise<ExtractionApiResponse>;

  // 恢复解压缩
  resumeExtraction: (taskId: string) => Promise<ExtractionApiResponse>;

  // 取消解压缩
  cancelExtraction: (taskId: string) => Promise<ExtractionApiResponse>;

  // 删除解压缩任务
  deleteTask: (taskId: string) => Promise<ExtractionApiResponse>;

  // 获取所有解压缩任务
  getAllTasks: () => Promise<ExtractionApiResponse>;

  // 获取指定解压缩任务
  getTask: (taskId: string) => Promise<ExtractionApiResponse>;

  // 获取活跃解压缩任务
  getActiveTasks: () => Promise<ExtractionApiResponse>;

  // 清理已完成的解压缩任务
  clearCompletedTasks: () => Promise<ExtractionApiResponse>;

  // 清空所有解压缩任务
  clearAllTasks: () => Promise<ExtractionApiResponse>;

  // 批量操作
  startBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
  pauseBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
  resumeBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;
  cancelBatch: (taskIds: string[]) => Promise<ExtractionApiResponse>;

  // 密码和冲突处理
  providePassword: (taskId: string, password: string) => Promise<ExtractionApiResponse>;
  handleFileConflict: (taskId: string, action: "overwrite" | "skip" | "rename") => Promise<ExtractionApiResponse>;
}

// 解压缩事件监听器接口
export interface ExtractionEventListeners {
  // 任务创建事件
  onExtractionTaskCreated?: (callback: (taskId: string, task: ExtractionTask) => void) => void;

  // 任务进度事件
  onExtractionTaskProgress?: (callback: (taskId: string, progress: number, extractedSize: number, totalSize?: number) => void) => void;

  // 任务状态变化事件
  onExtractionTaskStatusChanged?: (callback: (taskId: string, status: string, error?: string) => void) => void;

  // 任务完成事件
  onExtractionTaskCompleted?: (callback: (taskId: string, extractPath: string) => void) => void;

  // 任务错误事件
  onExtractionTaskError?: (callback: (taskId: string, error: string) => void) => void;

  // 密码需求事件
  onExtractionPasswordRequired?: (callback: (taskId: string, callback: (password?: string) => void) => void) => void;

  // 文件冲突事件
  onExtractionFileConflict?: (callback: (taskId: string, filePath: string, callback: (action: "overwrite" | "skip" | "rename") => void) => void) => void;
}

// 简化的解压缩 API 接口（整合了 API 调用和事件监听器）
export interface SimplifiedExtractionPreloadApi extends ExtractionPreloadApi, ExtractionEventListeners {}

/**
 * 创建解压缩 API
 */
export function createExtractionApi(): ExtractionPreloadApi {
  return {
    // 创建解压缩任务
    createTask: (archivePath: string, extractPath?: string, options?) => 
      ipcRenderer.invoke("extraction-create-task", archivePath, extractPath, options),

    // 通过对话框选择解压路径创建任务
    createTaskWithDialog: (archivePath: string, options?) => 
      ipcRenderer.invoke("extraction-create-task-with-dialog", archivePath, options),

    // 开始解压缩
    startExtraction: (taskId: string) => 
      ipcRenderer.invoke("extraction-start", taskId),

    // 暂停解压缩
    pauseExtraction: (taskId: string) => 
      ipcRenderer.invoke("extraction-pause", taskId),

    // 恢复解压缩
    resumeExtraction: (taskId: string) => 
      ipcRenderer.invoke("extraction-resume", taskId),

    // 取消解压缩
    cancelExtraction: (taskId: string) => 
      ipcRenderer.invoke("extraction-cancel", taskId),

    // 删除解压缩任务
    deleteTask: (taskId: string) => 
      ipcRenderer.invoke("extraction-delete-task", taskId),

    // 获取所有解压缩任务
    getAllTasks: () => 
      ipcRenderer.invoke("extraction-get-all-tasks"),

    // 获取指定解压缩任务
    getTask: (taskId: string) => 
      ipcRenderer.invoke("extraction-get-task", taskId),

    // 获取活跃解压缩任务
    getActiveTasks: () => 
      ipcRenderer.invoke("extraction-get-active-tasks"),

    // 清理已完成的解压缩任务
    clearCompletedTasks: () => 
      ipcRenderer.invoke("extraction-clear-completed-tasks"),

    // 清空所有解压缩任务
    clearAllTasks: () => 
      ipcRenderer.invoke("extraction-clear-all-tasks"),

    // 批量开始解压缩
    startBatch: (taskIds: string[]) => 
      ipcRenderer.invoke("extraction-start-batch", taskIds),

    // 批量暂停解压缩
    pauseBatch: (taskIds: string[]) => 
      ipcRenderer.invoke("extraction-pause-batch", taskIds),

    // 批量恢复解压缩
    resumeBatch: (taskIds: string[]) => 
      ipcRenderer.invoke("extraction-resume-batch", taskIds),

    // 批量取消解压缩
    cancelBatch: (taskIds: string[]) => 
      ipcRenderer.invoke("extraction-cancel-batch", taskIds),

    // 提供密码
    providePassword: (taskId: string, password: string) => 
      ipcRenderer.invoke("extraction-provide-password", taskId, password),

    // 处理文件冲突
    handleFileConflict: (taskId: string, action: "overwrite" | "skip" | "rename") => 
      ipcRenderer.invoke("extraction-handle-file-conflict", taskId, action),
  };
}

/**
 * 创建解压缩事件监听器
 */
export function createExtractionEventListeners(): ExtractionEventListeners {
  return {
    // 任务创建事件
    onExtractionTaskCreated: (callback) => {
      ipcRenderer.on("extraction-task-created", (_event, taskId, task) => callback(taskId, task));
    },

    // 任务进度事件
    onExtractionTaskProgress: (callback) => {
      ipcRenderer.on("extraction-task-progress", (_event, taskId, progress, extractedSize, totalSize) => 
        callback(taskId, progress, extractedSize, totalSize));
    },

    // 任务状态变化事件
    onExtractionTaskStatusChanged: (callback) => {
      ipcRenderer.on("extraction-task-status-changed", (_event, taskId, status, error) => 
        callback(taskId, status, error));
    },

    // 任务完成事件
    onExtractionTaskCompleted: (callback) => {
      ipcRenderer.on("extraction-task-completed", (_event, taskId, extractPath) => 
        callback(taskId, extractPath));
    },

    // 任务错误事件
    onExtractionTaskError: (callback) => {
      ipcRenderer.on("extraction-task-error", (_event, taskId, error) => callback(taskId, error));
    },

    // 密码需求事件
    onExtractionPasswordRequired: (callback) => {
      ipcRenderer.on("extraction-password-required", (_event, taskId, responseCallback) => 
        callback(taskId, responseCallback));
    },

    // 文件冲突事件
    onExtractionFileConflict: (callback) => {
      ipcRenderer.on("extraction-file-conflict", (_event, taskId, filePath, responseCallback) => 
        callback(taskId, filePath, responseCallback));
    },
  };
}

/**
 * 创建简化的解压缩 API（整合 API 和事件监听器）
 */
export function createSimplifiedExtractionPreloadApi(): SimplifiedExtractionPreloadApi {
  const api = createExtractionApi();
  const listeners = createExtractionEventListeners();

  return {
    ...api,
    ...listeners,
  };
}
