<template>
  <div class="py-2">
    <!-- 子路由显示区域 -->
    <router-view v-slot="{ Component }">
      <component :is="Component" v-if="Component" />
      <!-- 文件夹网格 -->
      <div v-else class="grid grid-cols-2 gap-4 p-4 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6">
        <div v-for="folder in sidebarStore.sidebarItems" :key="folder.name" @click="navigateToFolder(folder)"
          class="p-4 rounded-lg border transition-all duration-200 cursor-pointer folder-card group border-border hover:border-primary hover:shadow-md">
          <div class="flex flex-col items-center space-y-3 text-center">
            <!-- 文件夹图标 -->
            <div class="p-3 rounded-lg transition-colors bg-muted group-hover:bg-primary/10">
              <component :is="folder.icon" class="w-8 h-8" :class="folder.iconColor" />
            </div>
            <!-- 文件夹名称 -->
            <div>
              <h4 class="text-sm font-medium transition-colors text-foreground group-hover:text-primary">{{
                folder.name }}</h4>
            </div>
          </div>
        </div>
      </div>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useSidebarStore, type SidebarItem } from '@/store/sidebar'

const sidebarStore = useSidebarStore()

const router = useRouter()

const navigateToFolder = (folder: SidebarItem) => {
  // 添加 categoryId 和 parentId 查询参数，确保 FolderView 能正确识别和加载目录内容
  router.push({
    path: folder.path,
    query: {
      categoryId: folder.category_id.toString(),
      parentId: folder.id.toString() // 使用文件夹自己的 id 作为 parentId
    }
  })
}
</script>

<style scoped>
.file-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
}

.file-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  cursor: pointer;
}

.file-item:hover {
  background-color: hsl(var(--accent));
}

.file-icon {
  width: 3rem;
  height: 3rem;
  margin-bottom: 0.5rem;
}


.folder-card {
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.folder-card:hover {
  transform: translateY(-2px);
}

@media (max-width: 640px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .folder-card {
    min-height: 140px;
  }
}
</style>