/**
 * 独立的认证存储模块
 * 避免在 preload 脚本中引入 main.ts
 */

// 全局认证token存储
let authToken: string | null = null;

/**
 * 获取当前认证token
 */
export function getAuthToken(): string | null {
  return authToken;
}

/**
 * 设置认证token
 */
export function setAuthToken(token: string | null): void {
  authToken = token;
}

/**
 * 清除认证token
 */
export function clearAuthToken(): void {
  authToken = null;
}

/**
 * 检查是否有有效的认证token
 */
export function hasValidToken(): boolean {
  return authToken !== null && authToken.length > 0;
}
