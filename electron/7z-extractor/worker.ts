import { parentPort } from "worker_threads";
import { spawn, ChildProcess } from "child_process";
// import { pathTo7zip } from "7zip-bin"; // 7zip-bin模块导出有问题，使用动态导入
import * as fs from "fs";
// import * as path from "path";

interface WorkerMessage {
  type: "start" | "pause" | "resume" | "cancel";
  taskId: string;
  data?: any;
}

interface WorkerTask {
  taskId: string;
  archivePath: string;
  extractPath: string;
  password?: string;
  overwrite?: boolean;
}

class ExtractionWorker {
  private currentTask: WorkerTask | null = null;
  private currentProcess: ChildProcess | null = null;
  private isPaused: boolean = false;
  private isCancelled: boolean = false;

  constructor() {
    this.setupMessageHandler();
  }

  /**
   * 获取7zip可执行文件路径
   */
  private get7zipPath(): string {
    try {
      // 尝试动态导入7zip-bin
      const sevenZipBin = require("7zip-bin");
      return sevenZipBin.path7za || sevenZipBin.pathTo7zip;
    } catch (error) {
      // 如果导入失败，使用手动路径
      const os = require("os");
      const path = require("path");
      const platform = os.platform();
      const arch = os.arch();

      if (platform === "darwin") {
        return path.join(__dirname, "..", "..", "node_modules", "7zip-bin", "mac", arch === "arm64" ? "arm64" : "x64", "7za");
      } else if (platform === "linux") {
        return path.join(__dirname, "..", "..", "node_modules", "7zip-bin", "linux", "x64", "7za");
      } else if (platform === "win32") {
        return path.join(__dirname, "..", "..", "node_modules", "7zip-bin", "win", "x64", "7za.exe");
      }

      throw new Error("不支持的平台");
    }
  }

  private setupMessageHandler(): void {
    if (!parentPort) {
      throw new Error("此脚本必须在Worker线程中运行");
    }

    parentPort.on("message", (message: WorkerMessage) => {
      this.handleMessage(message);
    });
  }

  private async handleMessage(message: WorkerMessage): Promise<void> {
    const { type, taskId, data } = message;

    try {
      switch (type) {
        case "start":
          await this.startExtraction(data as WorkerTask);
          break;

        case "pause":
          this.pauseExtraction();
          break;

        case "resume":
          this.resumeExtraction();
          break;

        case "cancel":
          this.cancelExtraction();
          break;

        default:
          console.warn(`未知的消息类型: ${type}`);
      }
    } catch (error) {
      this.sendMessage({
        type: "error",
        taskId,
        data: { error: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  private async startExtraction(task: WorkerTask): Promise<void> {
    this.currentTask = task;
    this.isPaused = false;
    this.isCancelled = false;

    try {
      // 检查源文件是否存在
      if (!fs.existsSync(task.archivePath)) {
        throw new Error(`压缩文件不存在: ${task.archivePath}`);
      }

      // 确保目标目录存在
      await fs.promises.mkdir(task.extractPath, { recursive: true });

      // 构建7z命令参数
      const args = [
        "x", // 解压命令
        task.archivePath,
        `-o${task.extractPath}`,
        "-y", // 自动确认所有提示
      ];

      // 如果有密码，添加密码参数
      if (task.password) {
        args.push(`-p${task.password}`);
      }

      // 如果要覆盖，添加覆盖参数
      if (task.overwrite) {
        args.push("-aoa"); // 覆盖所有文件
      }

      // 获取7zip路径
      const pathTo7zip = this.get7zipPath();

      console.log(`Worker执行7z命令: ${pathTo7zip} ${args.join(" ")}`);

      // 启动7z进程
      this.currentProcess = spawn(pathTo7zip, args, {
        stdio: ["pipe", "pipe", "pipe"],
      });

      this.setupProcessHandlers();
    } catch (error) {
      this.sendMessage({
        type: "error",
        taskId: task.taskId,
        data: { error: error instanceof Error ? error.message : String(error) },
      });
    }
  }

  private setupProcessHandlers(): void {
    if (!this.currentProcess || !this.currentTask) return;

    const task = this.currentTask;
    let outputBuffer = "";
    let errorBuffer = "";
    let lastProgress = 0;

    // 处理标准输出
    this.currentProcess.stdout?.on("data", (data: Buffer) => {
      if (this.isPaused || this.isCancelled) return;

      outputBuffer += data.toString();
      const progress = this.parseProgress(outputBuffer);

      // 只在进度有显著变化时发送更新
      if (progress !== lastProgress && progress >= 0) {
        lastProgress = progress;
        this.sendMessage({
          type: "progress",
          taskId: task.taskId,
          data: {
            progress,
            extractedSize: 0, // 7z不提供准确的已解压大小
            totalSize: 0,
          },
        });
      }
    });

    // 处理错误输出
    this.currentProcess.stderr?.on("data", (data: Buffer) => {
      errorBuffer += data.toString();
      console.warn(`7z stderr: ${data.toString()}`);
    });

    // 处理进程结束
    this.currentProcess.on("close", (code: number) => {
      if (this.isCancelled) {
        return; // 已取消，不处理结果
      }

      if (code === 0) {
        this.sendMessage({
          type: "completed",
          taskId: task.taskId,
          data: { extractPath: task.extractPath },
        });
      } else {
        const error = errorBuffer || `解压缩失败，退出代码: ${code}`;
        this.sendMessage({
          type: "error",
          taskId: task.taskId,
          data: { error },
        });
      }

      this.cleanup();
    });

    // 处理进程错误
    this.currentProcess.on("error", (error: Error) => {
      if (!this.isCancelled) {
        this.sendMessage({
          type: "error",
          taskId: task.taskId,
          data: { error: `进程错误: ${error.message}` },
        });
      }
      this.cleanup();
    });
  }

  private parseProgress(output: string): number {
    // 解析7z输出中的进度信息
    // 7z的输出格式可能因版本而异，这里提供一个基本的解析逻辑

    const lines = output.split("\n");
    for (const line of lines.reverse()) {
      // 从最新的行开始解析
      // 查找百分比模式
      const percentMatch = line.match(/(\d+)%/);
      if (percentMatch) {
        return parseInt(percentMatch[1]);
      }

      // 查找文件计数模式（如果有的话）
      const fileMatch = line.match(/(\d+)\/(\d+)/);
      if (fileMatch) {
        const current = parseInt(fileMatch[1]);
        const total = parseInt(fileMatch[2]);
        return Math.round((current / total) * 100);
      }
    }

    return -1; // 无法解析进度
  }

  private pauseExtraction(): void {
    this.isPaused = true;

    if (this.currentProcess) {
      // 发送SIGSTOP信号暂停进程（在支持的系统上）
      try {
        this.currentProcess.kill("SIGSTOP");
      } catch (error) {
        console.warn("暂停进程失败:", error);
      }
    }
  }

  private resumeExtraction(): void {
    this.isPaused = false;

    if (this.currentProcess) {
      // 发送SIGCONT信号恢复进程（在支持的系统上）
      try {
        this.currentProcess.kill("SIGCONT");
      } catch (error) {
        console.warn("恢复进程失败:", error);
      }
    }
  }

  private cancelExtraction(): void {
    this.isCancelled = true;

    if (this.currentProcess) {
      try {
        this.currentProcess.kill("SIGKILL");
      } catch (error) {
        console.warn("终止进程失败:", error);
      }
    }

    this.cleanup();
  }

  private cleanup(): void {
    this.currentProcess = null;
    this.currentTask = null;
    this.isPaused = false;
    this.isCancelled = false;
  }

  private sendMessage(message: any): void {
    if (parentPort) {
      parentPort.postMessage(message);
    }
  }
}

// 启动Worker
new ExtractionWorker();
