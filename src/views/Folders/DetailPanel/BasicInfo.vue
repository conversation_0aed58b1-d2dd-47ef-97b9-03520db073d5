<template>
  <div class="p-4 space-y-3 border-b">
    <h5 class="text-sm font-medium text-muted-foreground">基础信息</h5>

    <div class="space-y-2">
      <!-- 文件大小或项目数 -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">
          {{ item.type === 'folder' ? '包含项目' : '文件大小' }}
        </span>
        <span class="text-sm font-medium">
          {{ getFileSize(item) }}
        </span>
      </div>

      <!-- 修改时间 -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">修改时间</span>
        <span class="text-sm font-medium">{{ formatDateTime(item.modifiedAt) }}</span>
      </div>

      <!-- 创建时间 -->
      <div v-if="item.createdAt" class="flex justify-between">
        <span class="text-sm text-muted-foreground">创建时间</span>
        <span class="text-sm font-medium">{{ formatDateTime(item.createdAt) }}</span>
      </div>

      <!-- 路径 -->
      <div v-if="item.path" class="flex justify-between">
        <span class="text-sm text-muted-foreground">路径</span>
        <span class="font-mono text-sm font-medium break-all">{{ item.path }}</span>
      </div>

      <!-- ID -->
      <div class="flex justify-between">
        <span class="text-sm text-muted-foreground">ID</span>
        <span class="font-mono text-sm font-medium">{{ item.id }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { ItemType, FileItemType } from '@/types/files'

// Props
defineProps<{
  item: ItemType
}>()

// 获取文件大小显示
const getFileSize = (item: ItemType): string => {
  if (item.type === 'folder') {
    return '/'
  }

  const fileItem = item as FileItemType
  // 优先使用后端提供的人类可读格式（确保不是空字符串）
  if (fileItem.size_human && fileItem.size_human.trim() !== '') {
    return fileItem.size_human
  }
  // 回退到原有的格式化大小
  return fileItem.size || '未知'
}

// 格式化日期时间
const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}
</script>