<template>
  <div class="p-6">
    <div class="mx-auto max-w-7xl">
      <!-- 标签页容器 -->
      <Tabs v-model="activeTab" class="w-full">
        <TabsList class="grid grid-cols-2 w-full">
          <TabsTrigger value="tags">标签设置</TabsTrigger>
          <TabsTrigger value="projects">游戏项目设置</TabsTrigger>
        </TabsList>

        <!-- 标签设置页面 -->
        <TabsContent value="tags" class="mt-6">
          <TagsManagement />
        </TabsContent>

        <!-- 游戏项目设置页面 -->
        <TabsContent value="projects" class="mt-6">
          <GameProjectsManagement />
        </TabsContent>
      </Tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import TagsManagement from './TagsManagement.vue'
import GameProjectsManagement from './GameProjectsManagement.vue'

// 当前活跃的标签页
const activeTab = ref('tags')
</script>