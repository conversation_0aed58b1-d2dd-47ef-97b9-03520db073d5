<template>
  <div class="p-4 space-y-3 border-b">
    <h5 class="text-sm font-medium text-muted-foreground">自定义属性</h5>

    <div class="space-y-3">
      <div v-for="(value, key) in properties" :key="key" class="grid grid-cols-[auto_1fr] gap-3 items-start">
        <!-- 属性标签 - 自适应宽度，不换行 -->
        <span class="text-sm whitespace-nowrap text-muted-foreground">
          {{ formatPropertyKey(key) }}
        </span>
        <!-- 属性值 - 可换行，占据剩余空间 -->
        <span class="text-sm font-medium text-right break-words" :class="getValueClass(value)">
          {{ formatPropertyValue(value) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
defineProps<{
  properties: Record<string, any>
}>()

// 格式化属性键名
const formatPropertyKey = (key: string): string => {
  // 直接返回键名，因为传入的已经是处理过的标签
  return key
}

// 格式化属性值用于显示
const formatPropertyValue = (value: any): string => {
  if (value === null || value === undefined) {
    return "无";
  }

  if (typeof value === "boolean") {
    return value ? "是" : "否";
  }

  if (typeof value === "object") {
    if (Array.isArray(value)) {
      return value.join(", ");
    }
    return JSON.stringify(value, null, 2);
  }

  if (typeof value === "number") {
    return value.toLocaleString("zh-CN");
  }

  // 处理特殊的字符串值
  if (typeof value === "string") {
    const trimmedValue = value.trim();
    // 处理 Y/N 值
    if (trimmedValue === "Y") return "是";
    if (trimmedValue === "N") return "否";
    // 处理其他布尔值字符串
    if (trimmedValue === "true" || trimmedValue === "True" || trimmedValue === "TRUE") return "是";
    if (trimmedValue === "false" || trimmedValue === "False" || trimmedValue === "FALSE") return "否";
    // 处理数字字符串的布尔值
    if (trimmedValue === "1") return "是";
    if (trimmedValue === "0") return "否";
  }

  return String(value);
}

// 获取值的样式类
const getValueClass = (value: any): string => {
  // 统一处理所有表示"是"的值
  const positiveValues = ["Y", "是", "true", "True", "TRUE", "有", "1", 1, true];
  // 统一处理所有表示"否"的值
  const negativeValues = ["N", "否", "false", "False", "FALSE", "无", "0", 0, false];

  if (typeof value === "boolean") {
    return value ? "text-green-600" : "text-red-600";
  }

  if (typeof value === "string") {
    const trimmedValue = value.trim();
    if (positiveValues.includes(trimmedValue)) {
      return "text-green-600";
    }
    if (negativeValues.includes(trimmedValue)) {
      return "text-red-600";
    }
  }

  if (typeof value === "number") {
    if (positiveValues.includes(value)) {
      return "text-green-600";
    }
    if (negativeValues.includes(value)) {
      return "text-red-600";
    }
    return "font-mono";
  }

  if (typeof value === "object") {
    return "font-mono text-xs";
  }

  return "";
}
</script>