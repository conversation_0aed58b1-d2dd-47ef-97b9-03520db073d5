import { ref, onUnmounted } from "vue";

/**
 * 搜索功能 Composable
 * 提供防抖搜索功能，避免频繁触发API请求
 */
export function useSearch(options: {
  /** 防抖延迟时间，默认300ms */
  debounceDelay?: number;
  /** 搜索回调函数 */
  onSearch: (query: string) => void;
}) {
  const { debounceDelay = 300, onSearch } = options;

  // 搜索状态
  const searchQuery = ref("");
  const isSearching = ref(false);

  // 防抖定时器
  let searchTimeout: NodeJS.Timeout | null = null;

  /**
   * 处理搜索输入
   * 使用防抖机制避免频繁触发搜索
   */
  const handleSearchInput = (query: string) => {
    searchQuery.value = query;

    // 清除之前的定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // 设置新的防抖定时器
    searchTimeout = setTimeout(() => {
      isSearching.value = true;
      try {
        onSearch(query.trim());
      } finally {
        isSearching.value = false;
      }
    }, debounceDelay);
  };

  /**
   * 立即执行搜索（不使用防抖）
   */
  const executeSearch = (query?: string) => {
    const searchTerm = query !== undefined ? query : searchQuery.value;

    // 清除防抖定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    isSearching.value = true;
    try {
      onSearch(searchTerm.trim());
    } finally {
      isSearching.value = false;
    }
  };

  /**
   * 清除搜索
   */
  const clearSearch = () => {
    searchQuery.value = "";

    // 清除防抖定时器
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // 立即执行空搜索
    executeSearch("");
  };

  /**
   * 设置搜索查询值（不触发搜索）
   */
  const setSearchQuery = (query: string) => {
    searchQuery.value = query;
  };

  /**
   * 清理定时器
   */
  const cleanup = () => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
      searchTimeout = null;
    }
  };

  // 组件卸载时清理定时器
  onUnmounted(cleanup);

  return {
    // 状态
    searchQuery,
    isSearching,

    // 方法
    handleSearchInput,
    executeSearch,
    clearSearch,
    setSearchQuery,
    cleanup,
  };
}

/**
 * 搜索功能类型定义
 */
export interface UseSearchReturn {
  searchQuery: ReturnType<typeof ref<string>>;
  isSearching: ReturnType<typeof ref<boolean>>;
  handleSearchInput: (query: string) => void;
  executeSearch: (query?: string) => void;
  clearSearch: () => void;
  setSearchQuery: (query: string) => void;
  cleanup: () => void;
}
