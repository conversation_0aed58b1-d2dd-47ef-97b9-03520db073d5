{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@electron/*": ["electron/*"]}, "types": ["node", "electron"]}, "include": ["src/**/*.ts", "src/**/*.vue", "src/**/*.d.ts", "src/types/**/*.d.ts", "electron/**/*.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "dist-electron"]}