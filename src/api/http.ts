import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse, type InternalAxiosRequestConfig } from "axios";
import config from "@/config";
import { getAuthToken, isLoggingOutState } from "@/composables/useAuth";

// 通用 API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  msg: string;
  data: T;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 错误响应类型
export interface ErrorResponse {
  code: number;
  message: string;
  details?: any;
}

// HTTP 状态码常量
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * 创建 Axios 实例
 */
function createAxiosInstance(): AxiosInstance {
  const instance = axios.create({
    baseURL: config.api.baseURL,
    timeout: config.api.timeout,
    headers: {
      "Content-Type": "application/json",
    },
  });

  // 请求拦截器
  instance.interceptors.request.use(
    (axiosConfig: InternalAxiosRequestConfig) => {
      // 添加认证 token
      const token = getAuthToken();
      if (token) {
        axiosConfig.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求ID用于追踪
      axiosConfig.headers["X-Request-ID"] = generateRequestId();

      return axiosConfig;
    },
    (error) => {
      console.error("❌ 请求配置错误:", error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 检查响应体中的业务错误码
      const { data } = response;

      // 检查业务层认证失败
      if (data && data.code === 401) {
        handleAuthError();
      }

      return response;
    },
    (error) => {
      // 处理网络错误和HTTP错误
      console.error("❌ API 错误:", error);

      if (error.response) {
        const { status, data } = error.response;

        switch (status) {
          case HTTP_STATUS.UNAUTHORIZED:
          case HTTP_STATUS.FORBIDDEN:
            // 避免与业务层认证失败重复处理
            if (!data || data.code !== 401) {
              handleAuthError();
            }
            break;
          case HTTP_STATUS.NOT_FOUND:
            console.warn("⚠️ 请求的资源不存在");
            break;
          case HTTP_STATUS.INTERNAL_SERVER_ERROR:
            console.error("💥 服务器内部错误");
            break;
        }

        // 返回结构化的错误信息
        return Promise.reject({
          code: status,
          message: data?.message || getErrorMessage(status),
          details: data,
        } as ErrorResponse);
      } else if (error.request) {
        // 网络错误
        return Promise.reject({
          code: 0,
          message: "网络连接失败，请检查网络设置",
          details: error.request,
        } as ErrorResponse);
      } else {
        // 其他错误
        return Promise.reject({
          code: -1,
          message: error.message || "未知错误",
          details: error,
        } as ErrorResponse);
      }
    }
  );

  return instance;
}

// 防止重复处理认证失败
let authErrorHandled = false;

/**
 * 处理认证错误
 * 通过事件机制通知应用层，避免循环依赖
 */
function handleAuthError(): void {
  // 如果正在退出登录，不触发认证失败处理，避免重定向循环
  if (isLoggingOutState()) {
    console.log("🚪 正在退出登录，跳过认证失败处理");
    return;
  }

  if (authErrorHandled) {
    return;
  }

  authErrorHandled = true;

  if (typeof window !== "undefined") {
    window.dispatchEvent(
      new CustomEvent("http-auth-failure", {
        detail: {
          source: "http-interceptor",
          message: "HTTP请求认证失败",
        },
      })
    );
  }

  // 重置标记，允许后续认证失败处理
  setTimeout(() => {
    authErrorHandled = false;
  }, 1000);
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * 根据状态码获取错误信息
 */
function getErrorMessage(status: number): string {
  const errorMessages: Record<number, string> = {
    [HTTP_STATUS.BAD_REQUEST]: "请求参数错误",
    [HTTP_STATUS.UNAUTHORIZED]: "认证失败，请重新登录",
    [HTTP_STATUS.FORBIDDEN]: "没有权限访问该资源",
    [HTTP_STATUS.NOT_FOUND]: "请求的资源不存在",
    [HTTP_STATUS.INTERNAL_SERVER_ERROR]: "服务器内部错误",
  };

  return errorMessages[status] || "请求失败";
}

// 创建默认的 HTTP 客户端实例
export const http = createAxiosInstance();

/**
 * HTTP 请求封装类
 */
export class HttpClient {
  private instance: AxiosInstance;

  constructor(config?: AxiosRequestConfig) {
    this.instance = axios.create(config);
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.get<ApiResponse<T>>(url, config);
    return response.data;
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.post<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.put<ApiResponse<T>>(url, data, config);
    return response.data;
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.delete<ApiResponse<T>>(url, config);
    return response.data;
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.instance.patch<ApiResponse<T>>(url, data, config);
    return response.data;
  }
}

// 导出便捷的请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) => http.get<ApiResponse<T>>(url, config).then((res) => res.data),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => http.post<ApiResponse<T>>(url, data, config).then((res) => res.data),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => http.put<ApiResponse<T>>(url, data, config).then((res) => res.data),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) => http.delete<ApiResponse<T>>(url, config).then((res) => res.data),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => http.patch<ApiResponse<T>>(url, data, config).then((res) => res.data),
};

export default http;
