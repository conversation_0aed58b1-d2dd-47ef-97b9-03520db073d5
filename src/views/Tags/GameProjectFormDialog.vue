<template>
  <Dialog :open="open" @update:open="$emit('update:open', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
        <DialogDescription>
          {{ isEdit ? '编辑游戏项目信息' : '创建新的游戏项目' }}
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <div class="space-y-2">
          <label for="name" class="text-sm font-medium">项目名称</label>
          <Input id="name" v-model="formData.name" placeholder="请输入游戏项目名称" required />
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" @click="handleCancel">
            取消
          </Button>
          <Button type="submit" :disabled="loading">
            <RefreshCw v-if="loading" class="w-4 h-4 mr-2 animate-spin" />
            {{ isEdit ? '更新' : '创建' }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { RefreshCw } from 'lucide-vue-next'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import type { GameProject, CreateGameProjectParams, UpdateGameProjectParams } from '@/api/services/tags'

interface Props {
  open: boolean
  title: string
  initialData?: GameProject | null
}

interface Emits {
  'update:open': [value: boolean]
  'submit': [data: CreateGameProjectParams] | [data: UpdateGameProjectParams]
}

const props = withDefaults(defineProps<Props>(), {
  initialData: null,
})

const emit = defineEmits<Emits>()

// 表单数据
const formData = ref({
  name: '',
})

// 加载状态
const loading = ref(false)

// 计算属性
const isEdit = computed(() => !!props.initialData)

// 重置表单
const resetForm = () => {
  formData.value = {
    name: '',
  }
}

// 监听初始数据变化
watch(
  () => props.initialData,
  (newData) => {
    if (newData) {
      formData.value = {
        name: newData.name,
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

// 监听对话框打开状态
watch(
  () => props.open,
  (isOpen) => {
    if (!isOpen) {
      resetForm()
    }
  }
)

// 处理提交
const handleSubmit = async () => {
  try {
    loading.value = true

    if (isEdit.value && props.initialData) {
      const updateData: UpdateGameProjectParams = {
        id: props.initialData.id,
        name: formData.value.name,
      }
      emit('submit', updateData)
    } else {
      const createData: CreateGameProjectParams = {
        name: formData.value.name,
      }
      emit('submit', createData)
    }
  } catch (error) {
    // 错误处理由父组件负责
  } finally {
    loading.value = false
  }
}

// 处理取消
const handleCancel = () => {
  emit('update:open', false)
}
</script>
