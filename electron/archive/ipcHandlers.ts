import { ipcMain, dialog } from "electron";
import type { ArchiveManager } from "./archiveManager";
import type { ArchiveApiResponse, ArchiveOptions, BatchPackingOptions } from "./types";

/**
 * 注册所有压缩相关的 IPC 处理器
 */
export function registerArchiveIpcHandlers(archiveManager: ArchiveManager) {
  // 智能打包分析
  ipcMain.handle(
    "archive-analyze-smart-packing",
    async (
      _event,
      filePaths: string[],
      options?: BatchPackingOptions
    ): Promise<ArchiveApiResponse> => {
      try {
        const analysis = await archiveManager.analyzeSmartPacking(filePaths, options);
        return { success: true, data: analysis };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 创建压缩任务
  ipcMain.handle(
    "archive-create-task",
    async (
      _event,
      sourcePaths: string[],
      options?: ArchiveOptions
    ): Promise<ArchiveApiResponse> => {
      try {
        const taskId = await archiveManager.createArchiveTask(sourcePaths, options);
        return { success: true, taskId };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 开始压缩任务
  ipcMain.handle(
    "archive-start-task",
    async (_event, taskId: string): Promise<ArchiveApiResponse> => {
      try {
        await archiveManager.startArchiveTask(taskId);
        return { success: true };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 取消压缩任务
  ipcMain.handle(
    "archive-cancel-task",
    async (_event, taskId: string): Promise<ArchiveApiResponse> => {
      try {
        await archiveManager.cancelArchiveTask(taskId);
        return { success: true };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 获取任务信息
  ipcMain.handle(
    "archive-get-task",
    async (_event, taskId: string): Promise<ArchiveApiResponse> => {
      try {
        const task = archiveManager.getTask(taskId);
        if (!task) {
          return { success: false, error: "任务不存在" };
        }
        return { success: true, data: task };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );

  // 获取所有任务
  ipcMain.handle("archive-get-all-tasks", async (): Promise<ArchiveApiResponse> => {
    try {
      const tasks = archiveManager.getAllTasks();
      return { success: true, data: tasks };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 清理已完成的任务
  ipcMain.handle("archive-cleanup-completed", async (): Promise<ArchiveApiResponse> => {
    try {
      await archiveManager.cleanupCompletedTasks();
      return { success: true };
    } catch (error) {
      return { success: false, error: String(error) };
    }
  });

  // 显示打包确认对话框
  ipcMain.handle(
    "archive-show-confirmation-dialog",
    async (_event, analysis: any): Promise<ArchiveApiResponse> => {
      try {
        const result = await dialog.showMessageBox({
          type: "question",
          buttons: ["确认打包", "逐个上传", "取消"],
          defaultId: 0,
          cancelId: 2,
          title: "智能批量打包",
          message: "检测到大量文件，建议使用批量打包优化上传",
          detail: `文件数量: ${analysis.fileCount} 个
文件大小: ${formatFileSize(analysis.totalSize)}
预计打包时间: ${analysis.estimatedPackTime} 秒
预计节省上传时间: ${analysis.estimatedSavings.uploadTime} 秒

${analysis.reason}`,
        });

        return {
          success: true,
          data: {
            choice: result.response, // 0: 确认打包, 1: 逐个上传, 2: 取消
            cancelled: result.response === 2,
          },
        };
      } catch (error) {
        return { success: false, error: String(error) };
      }
    }
  );
}

/**
 * 注销所有压缩相关的 IPC 处理器
 */
export function unregisterArchiveIpcHandlers() {
  const handlers = [
    "archive-analyze-smart-packing",
    "archive-create-task",
    "archive-start-task",
    "archive-cancel-task",
    "archive-get-task",
    "archive-get-all-tasks",
    "archive-cleanup-completed",
    "archive-show-confirmation-dialog",
  ];

  handlers.forEach((handler) => {
    ipcMain.removeAllListeners(handler);
  });
}

// 辅助函数
function formatFileSize(bytes: number): string {
  const units = ["B", "KB", "MB", "GB"];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}
